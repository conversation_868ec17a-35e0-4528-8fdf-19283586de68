#!/usr/bin/env python3
"""
Test script to verify that only configured channels are being monitored
and all other channels are properly excluded.
"""

import json
import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from signal_handler import SignalHandler
    from config_manager import ConfigManager
    print("✅ Successfully imported signal handler modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

def test_channel_configuration():
    """Test the channel configuration and filtering"""
    print("🔍 TESTING CHANNEL CONFIGURATION AND FILTERING")
    print("=" * 60)
    
    # Load configuration
    try:
        with open('finalconfig.json', 'r') as f:
            config_data = json.load(f)
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return
    
    # Get channel settings
    telegram_settings = config_data.get('telegram_settings', {})
    target_channels = telegram_settings.get('target_channels', [])
    excluded_channels = telegram_settings.get('excluded_channels', [])
    
    print(f"\n📋 CONFIGURATION SUMMARY:")
    print(f"Target channels: {target_channels}")
    print(f"Excluded channels: {excluded_channels}")
    print(f"Total target channels: {len(target_channels)}")
    print(f"Total excluded channels: {len(excluded_channels)}")
    
    # Verify no overlap between target and excluded
    overlap = set(target_channels) & set(excluded_channels)
    if overlap:
        print(f"⚠️  WARNING: Overlap between target and excluded channels: {overlap}")
    else:
        print("✅ No overlap between target and excluded channels")
    
    return target_channels, excluded_channels

def test_signal_handler_initialization():
    """Test signal handler initialization with new configuration"""
    print(f"\n🔍 TESTING SIGNAL HANDLER INITIALIZATION")
    print("=" * 60)
    
    try:
        config_manager = ConfigManager()
        signal_handler = SignalHandler(config_manager)
        
        print(f"✅ Signal handler initialized successfully")
        print(f"Target channels: {signal_handler.target_channels}")
        print(f"Excluded channels: {signal_handler.excluded_channels}")
        print(f"Channel health tracking: {list(signal_handler.channel_health.keys())}")
        
        # Verify channel health only tracks target channels
        health_channels = set(signal_handler.channel_health.keys())
        target_channels = set(signal_handler.target_channels)
        
        if health_channels == target_channels:
            print("✅ Channel health tracking matches target channels exactly")
        else:
            print(f"⚠️  Channel health tracking mismatch:")
            print(f"   Health channels: {health_channels}")
            print(f"   Target channels: {target_channels}")
            print(f"   Extra in health: {health_channels - target_channels}")
            print(f"   Missing from health: {target_channels - health_channels}")
        
        return signal_handler
        
    except Exception as e:
        print(f"❌ Failed to initialize signal handler: {e}")
        return None

def test_message_filtering(signal_handler):
    """Test message filtering logic"""
    print(f"\n🔍 TESTING MESSAGE FILTERING LOGIC")
    print("=" * 60)
    
    # Test channels
    test_channels = [
        (-1001509251052, "Solana Gems Radar", "TARGET - should be processed"),
        (-1002093384030, "Solana Early Trending", "EXCLUDED - should be ignored"),
        (-1002177594166, "OxyZen Calls", "EXCLUDED - should be ignored"),
        (-1002428819353, "Solbix Community Calls", "EXCLUDED - should be ignored"),
        (-1001938040677, "GMGN", "EXCLUDED - should be ignored"),
        (-1002225558516, "Pumpfun Volume Alert", "EXCLUDED - should be ignored"),
        (-**********, "Unknown Channel", "UNKNOWN - should be ignored"),
    ]
    
    for channel_id, channel_name, expected_behavior in test_channels:
        print(f"\nTesting {channel_name} ({channel_id}):")
        print(f"Expected: {expected_behavior}")
        
        # Test target channel check
        in_target = channel_id in signal_handler.target_channels
        in_excluded = channel_id in signal_handler.excluded_channels
        in_health = channel_id in signal_handler.channel_health
        
        print(f"  In target_channels: {in_target}")
        print(f"  In excluded_channels: {in_excluded}")
        print(f"  In channel_health: {in_health}")
        
        # Determine if message would be processed
        would_process = in_target and not in_excluded
        
        if "TARGET" in expected_behavior and would_process:
            print(f"  ✅ CORRECT: Would be processed")
        elif "EXCLUDED" in expected_behavior and not would_process:
            print(f"  ✅ CORRECT: Would be ignored")
        elif "UNKNOWN" in expected_behavior and not would_process:
            print(f"  ✅ CORRECT: Would be ignored")
        else:
            print(f"  ❌ INCORRECT: Processing behavior doesn't match expectation")

def test_channel_name_mapping(signal_handler):
    """Test channel name mapping logic"""
    print(f"\n🔍 TESTING CHANNEL NAME MAPPING")
    print("=" * 60)
    
    # Test the channel name mapping logic from _extract_signal
    test_message = {
        'text': 'Test message with token: 7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump',
        'chat_id': -1001509251052,  # Solana Gems Radar
        'date': None,
        'is_gmgn_channel': False,
        'channel_identifier': None
    }
    
    print(f"Testing channel name mapping for Solana Gems Radar ({test_message['chat_id']}):")
    
    # Simulate the channel name mapping logic
    channel_names = {}
    for channel_id in signal_handler.target_channels:
        if channel_id == -1001509251052:
            channel_names[channel_id] = "Solana Gems Radar"
        else:
            channel_names[channel_id] = f"Channel {channel_id}"
    
    if test_message['chat_id'] in channel_names:
        mapped_name = channel_names[test_message['chat_id']]
        print(f"  ✅ Channel mapped to: {mapped_name}")
    else:
        print(f"  ❌ Channel not found in mapping")
    
    print(f"\nAll mapped channels: {channel_names}")

def main():
    """Run all tests"""
    print("🧪 CHANNEL FILTERING AND CONFIGURATION TEST")
    print("=" * 60)
    
    # Test 1: Configuration
    target_channels, excluded_channels = test_channel_configuration()
    
    # Test 2: Signal handler initialization
    signal_handler = test_signal_handler_initialization()
    
    if signal_handler:
        # Test 3: Message filtering
        test_message_filtering(signal_handler)
        
        # Test 4: Channel name mapping
        test_channel_name_mapping(signal_handler)
    
    print(f"\n🏁 Testing completed!")
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print(f"✅ Only Solana Gems Radar (-1001509251052) should be monitored")
    print(f"✅ All other channels should be excluded and ignored")
    print(f"✅ Channel health tracking should only include target channels")
    print(f"✅ Message filtering should reject excluded and unknown channels")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"1. Restart your bot to apply the new configuration")
    print(f"2. Monitor logs to confirm only Solana Gems Radar messages are processed")
    print(f"3. Check that excluded channels are properly ignored")

if __name__ == "__main__":
    main()
