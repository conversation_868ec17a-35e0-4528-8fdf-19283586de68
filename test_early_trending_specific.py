#!/usr/bin/env python3
"""
Specific test for Solana Early Trending channel token extraction logic
"""

import re
import json
import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from signal_handler import SignalHandler
    from config_manager import ConfigManager
    print("✅ Successfully imported signal handler modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

def test_early_trending_detection():
    """Test how the Solana Early Trending channel is detected"""
    print("🔍 TESTING SOLANA EARLY TRENDING CHANNEL DETECTION")
    print("=" * 60)
    
    # Initialize signal handler
    config_manager = ConfigManager()
    signal_handler = SignalHandler(config_manager)
    
    # Check what the solearlytrending_channel_id is set to
    early_trending_id = signal_handler.solearlytrending_channel_id
    print(f"solearlytrending_channel_id from config: {early_trending_id}")
    print(f"Type: {type(early_trending_id)}")
    
    # Expected channel ID for Solana Early Trending
    expected_channel_id = -1002093384030
    print(f"Expected channel ID: {expected_channel_id}")
    
    # Check if they match
    if early_trending_id == expected_channel_id:
        print("✅ Channel ID matches - special logic should be triggered")
    else:
        print("❌ Channel ID doesn't match - special logic will NOT be triggered")
        print("This explains why all formats are working!")
    
    return early_trending_id, expected_channel_id

def test_soul_sniper_patterns():
    """Test different soul_sniper_bot URL patterns"""
    print("\n🔍 TESTING SOUL SNIPER URL PATTERNS")
    print("=" * 60)
    
    test_urls = [
        ("https://t.me/soul_sniper_bot?start=15_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump", "CORRECT - start=15_"),
        ("https://t.me/soul_sniper_bot?start=123_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump", "WRONG - start=123_"),
        ("https://t.me/soul_sniper_bot?start=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump", "WRONG - no prefix"),
        ("https://t.me/soul_sniper_bot?start=15_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bk", "WRONG - no pump suffix"),
    ]
    
    # The exact pattern from the code
    soul_sniper_pattern = r'https://t\.me/soul_sniper_bot\?start=15_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
    
    for url, description in test_urls:
        match = re.search(soul_sniper_pattern, url)
        if match:
            token = match.group(1)
            print(f"✅ {description}: {token}")
        else:
            print(f"❌ {description}: No match")

def test_channel_specific_extraction():
    """Test extraction with specific channel info"""
    print("\n🔍 TESTING CHANNEL-SPECIFIC EXTRACTION")
    print("=" * 60)
    
    config_manager = ConfigManager()
    signal_handler = SignalHandler(config_manager)
    
    # Test messages
    test_messages = [
        {
            "text": "Check it out: https://t.me/soul_sniper_bot?start=15_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
            "description": "CORRECT soul_sniper format"
        },
        {
            "text": "Check it out: https://t.me/soul_sniper_bot?start=123_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
            "description": "WRONG soul_sniper format (123_ instead of 15_)"
        },
        {
            "text": "CA: 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
            "description": "Direct CA format (should be rejected for Early Trending)"
        }
    ]
    
    # Test with the actual Early Trending channel ID
    early_trending_id = -1002093384030
    channel_info = {'channel_id': early_trending_id}
    
    print(f"Testing with channel_id: {early_trending_id}")
    print(f"signal_handler.solearlytrending_channel_id: {signal_handler.solearlytrending_channel_id}")
    
    for msg in test_messages:
        text = msg["text"]
        description = msg["description"]
        
        print(f"\nTest: {description}")
        print(f"Message: {text}")
        
        # Test extract_token_address method
        try:
            token = signal_handler.extract_token_address(text, channel_info)
            if token:
                print(f"  extract_token_address: ✅ {token}")
            else:
                print(f"  extract_token_address: ❌ No token found")
        except Exception as e:
            print(f"  extract_token_address: ❌ ERROR: {e}")
        
        # Test _extract_token_addresses method
        try:
            tokens = signal_handler._extract_token_addresses(text, early_trending_id)
            if tokens:
                print(f"  _extract_token_addresses: ✅ {tokens[0]}")
            else:
                print(f"  _extract_token_addresses: ❌ No tokens found")
        except Exception as e:
            print(f"  _extract_token_addresses: ❌ ERROR: {e}")

def main():
    """Run all tests"""
    print("🧪 SOLANA EARLY TRENDING CHANNEL SPECIFIC TESTS")
    print("=" * 60)
    
    # Test 1: Channel detection
    early_trending_id, expected_id = test_early_trending_detection()
    
    # Test 2: Soul sniper patterns
    test_soul_sniper_patterns()
    
    # Test 3: Channel-specific extraction
    test_channel_specific_extraction()
    
    print(f"\n🏁 Testing completed!")
    
    # Summary
    print(f"\n📋 SUMMARY:")
    if early_trending_id == expected_id:
        print("✅ Channel ID is correctly configured")
        print("✅ Special Early Trending logic should be active")
        print("❓ If signals are still being missed, check:")
        print("   - Signal processing is enabled")
        print("   - Message format matches soul_sniper_bot?start=15_ pattern")
        print("   - No cooldowns are blocking the channel")
    else:
        print("❌ Channel ID mismatch - this explains the issue!")
        print(f"   Expected: {expected_id}")
        print(f"   Actual: {early_trending_id}")
        print("🔧 Fix: Add 'solearlytrending_channel_id': -1002093384030 to telegram_settings in config")

if __name__ == "__main__":
    main()
