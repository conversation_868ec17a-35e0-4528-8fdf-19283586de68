{"network_mode": "MAINNET", "_comment_api_rate_limits": "DexScreener: 295 RPM (4.9 RPS), Helius RPC: 3000 RPM (50 RPS) with smart retry logic", "api_rate_limits": {"dexscreener_rpm": 300, "dexscreener_rps": 5, "helius_rpc_rpm": 3000, "helius_retry_delay_ms": 300, "helius_max_retries": 3, "coingecko_rps": 10.0, "request_spacing_buffer_ms": 50}, "api_endpoints": {"dexscreener": "https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}", "_comment_devnet_removed": "Devnet support removed - mainnet only", "helius_rpc": "https://mainnet.helius-rpc.com/?api-key={api_key}", "pumpportal_api": "https://pumpportal.fun/api/trade-local", "coingecko_sol_price": "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd", "solana_rpc": "https://api.mainnet-beta.solana.com"}, "pump_fun_settings": {"graduation_target": 69000, "virtual_liquidity_multiplier": 2.0, "default_sol_price": 150.0, "request_timeout": 10, "request_headers": {"Accept": "*/*"}}, "telegram_settings": {"target_channel_id": "0", "bot_session_name": "trading_real_session", "target_channels": [-1001509251052], "excluded_channels": [-1001938040677, -1002093384030, -1002187518673, -1002017857449, -1002177594166, -1002017173747, -1002428819353, -1002225558516], "bot_info_channel_id": -1002525039395, "direct_signal_processing": true}, "wallet_settings": {"default_wallet_name": "TEST1", "auto_select_wallet": true, "wallet_rotation": false, "max_trades_per_wallet": 500}, "trading_settings": {"signal_listener_poll_seconds": 0.1, "ws_reconnect_delay_seconds": 10, "adaptive_position_monitoring": true, "position_monitoring_intervals": {"critical_risk_ms": 800, "high_risk_ms": 900, "medium_risk_ms": 1000, "low_risk_ms": 1100}, "risk_detection_thresholds": {"critical_pnl_percent": -12.0, "critical_profit_percent": 40.0, "critical_volatility": 0.08, "critical_drawdown_percent": -8.0, "critical_age_minutes": 1.5, "high_pnl_loss_percent": -8.0, "high_pnl_profit_percent": 25.0, "high_volatility": 0.04, "high_drawdown_percent": -4.0, "high_age_minutes": 3.0, "high_profit_drop_threshold": 1.15, "high_profit_drop_ratio": 0.92, "medium_pnl_loss_percent": -4.0, "medium_pnl_profit_percent": 12.0, "medium_volatility": 0.015, "medium_age_minutes": 10.0, "medium_profit_drop_threshold": 1.08, "medium_profit_drop_ratio": 0.96}, "profit_taking": {"enabled": false, "levels": [{"percent": 5.0, "sell_fraction": 0.6}, {"percent": 10.0, "sell_fraction": 0.5}, {"percent": 15.0, "sell_fraction": 1.0}]}, "indicator_price_points": 30, "sol_trade_amount": 0.15, "total_sol_capital": 0.4, "min_wallet_balance": 0.5, "trade_cooldown_seconds": 5, "max_concurrent_trades": 2, "max_hold_time_minutes": 15, "enforce_max_hold_time": true, "confidence_threshold_for_tg_buy": 0.4, "liquidity_thresholds": {"min_usd": 8000.0, "safe_entry_usd": 8000.0, "absolute_minimum_usd": 7500.0}, "_comment_max_age": "Max age disabled - volume/liquidity/mc/txn activity matters more than age", "fdv_surge_threshold_percent": 150, "prevent_buy_if_x_multiplier": true, "rug_protection": {"enabled": true, "min_liquidity_usd": 20000.0, "min_volume_5m_usd": 6000.0, "min_fdv_usd": 10000, "min_market_cap_usd": 12000, "max_market_cap_usd": 60000, "max_top10_holders_percent": 35.0, "min_token_age_minutes": 0, "max_token_age_minutes": 1.5, "check_blacklist": true, "check_mintable": false, "check_dev_wallet_activity": true, "dev_sell_confidence_penalty": 0.3, "honeypot_protection": true, "whale_concentration_protection": true, "instant_rug_protection": true, "realtime_monitoring": {"liquidity_drain_threshold_percent": 30.0, "holder_exodus_threshold_percent": 20.0, "whale_concentration_increase_threshold_percent": 10.0, "enable_liquidity_monitoring": true, "enable_holder_monitoring": true, "enable_whale_monitoring": true}}, "default_strategy_name": "AGGRESSIVE", "strategies": {"DEFAULT": {"take_profit_percent": 20.0, "stop_loss_percent": -25.0, "min_confidence": 0.6, "tp_min_percent": 12.0, "tp_max_percent": 18.0, "use_adaptive_tp_sl": true, "slippage_buffer_percent": 6}, "SAFE": {"take_profit_percent": 15.0, "stop_loss_percent": -20.0, "min_confidence": 0.6, "tp_min_percent": 12.0, "tp_max_percent": 18.0, "use_adaptive_tp_sl": true, "slippage_buffer_percent": 5}, "AGGRESSIVE": {"take_profit_percent": 30.0, "stop_loss_percent": -15.0, "min_confidence": 0.5, "tp_min_percent": 60.0, "tp_max_percent": 100.0, "use_adaptive_tp_sl": false, "take_profit_percent_range": [40, 80], "stop_loss_percent_range": [-30, -20], "slippage_buffer_percent": 6}, "ULTRA_CONSERVATIVE": {"take_profit_percent": 10.0, "stop_loss_percent": -20.0, "min_confidence": 0.7, "tp_min_percent": 8.0, "tp_max_percent": 12.0, "use_adaptive_tp_sl": true, "take_profit_percent_range": [10, 20], "stop_loss_percent_range": [-25, -20], "slippage_buffer_percent": 4}}, "transaction_settings": {"slippage_percent": 6.0, "buy_tip_sol": 0.0001, "handling_fee_percent": 0.0, "gas_price_sol": 0.0001, "platform_fee_percent": 0.5, "pumpfun_fee_percent": 1.0, "use_adaptive_slippage": true, "use_dynamic_priority_fees": true, "max_priority_fee_multiplier": 3.0, "small_trade_threshold_sol": 0.01, "max_sell_retries": 2}, "num_analysis_workers": 3, "num_execution_workers": 4, "api_request_timeout": 2.0, "position_monitor_interval_seconds": 1.1, "api_request_spacing_ms": 200, "requests_per_monitoring_cycle": 3, "sol_price_cache_minutes": 1, "token_analysis_cache_minutes": 2, "_comment_helius_rate_limiting": "Helius RPC rate limiting: 3000 RPM (50 RPS) with 100ms spacing and smart retry logic", "helius_rpc_rate_limit": {"min_request_spacing_ms": 100, "retry_delay_ms": 200, "max_retries": 3, "backoff_multiplier": 1.5, "circuit_breaker_threshold": 10, "circuit_breaker_timeout_seconds": 30}}}