"""
Unified Trading Interface
Real mainnet trading only:
- PumpPortal for mainnet pump.fun tokens
- Helius for SPL tokens (if needed)
"""

import logging
from typing import Dict, Any
from enum import Enum

logger = logging.getLogger(__name__)

class NetworkMode(Enum):
    MAINNET = "MAINNET"

class TradingMode(Enum):
    PUMP_FUN = "PUMP_FUN"      # PumpPortal for pump.fun tokens
    SPL_TOKEN = "SPL_TOKEN"    # Helius for SPL tokens

class UnifiedTrader:
    """
    Unified trading interface for real mainnet trading:
    - Mainnet + pump.fun tokens -> PumpPortal
    - SPL tokens -> He<PERSON> (if needed)
    """

    def __init__(self, config_manager, state_manager=None, bot_controller=None):
        self.config_manager = config_manager
        self.state_manager = state_manager  # ENTERPRISE FIX: Direct state manager reference
        self.bot_controller = bot_controller  # CRITICAL FIX: Access to adaptive slippage system
        self.network_mode = NetworkMode.MAINNET
        self.current_trading_mode = TradingMode.PUMP_FUN

        # Initialize traders
        self.pumpportal_trader = None
        self.helius_trader = None

        logger.info("Unified Trader initialized for mainnet real trading with adaptive slippage support")

    def get_adaptive_slippage(self, token_address: str = None, market_data: dict = None) -> float:
        """
        Get adaptive slippage using the bot controller's slippage calculation system.
        This ensures consistent slippage calculation across all trading operations.
        """
        try:
            if self.bot_controller and hasattr(self.bot_controller, 'get_strategy_slippage'):
                # Use bot controller's adaptive slippage system
                slippage_decimal = self.bot_controller.get_strategy_slippage(token_address, market_data)
                slippage_percent = slippage_decimal * 100.0
                logger.info(f"🎯 UNIFIED ADAPTIVE SLIPPAGE: {slippage_percent:.1f}% for {token_address or 'trade'}")
                return slippage_percent
            else:
                # Fallback: Get from config directly
                transaction_settings = self.config_manager.get_section('transaction_settings')
                base_slippage = transaction_settings.get('slippage_percent', 10.0)
                logger.warning(f"Bot controller not available, using base slippage: {base_slippage}%")
                return base_slippage

        except Exception as e:
            logger.error(f"Error getting adaptive slippage: {e}")
            return 10.0  # Safe fallback

    async def initialize_traders(self):
        """Initialize the appropriate traders based on configuration"""
        try:
            # Initialize PumpPortal trader for mainnet
            try:
                from pumpportal_trader import PumpPortalTrader
                self.pumpportal_trader = PumpPortalTrader(self.config_manager)
                logger.info("✅ PumpPortal trader initialized for mainnet")
            except Exception as pumpportal_error:
                logger.error(f"❌ Failed to initialize PumpPortal trader: {pumpportal_error}")
                logger.error(f"❌ PumpPortal error details: {type(pumpportal_error).__name__}: {str(pumpportal_error)}")
                self.pumpportal_trader = None
                # Continue to try Helius

            # Helius trader disabled (devnet support removed)
            self.helius_trader = None
            logger.info("ℹ️ Helius trader disabled - devnet support removed")
                # Don't fail completely - PumpPortal can still work for mainnet

            # Check if at least one trader is available
            if self.pumpportal_trader is None and self.helius_trader is None:
                logger.error("❌ CRITICAL: No traders available - both PumpPortal and Helius failed to initialize")
                raise Exception("No traders available")
            elif self.pumpportal_trader is None:
                logger.warning("⚠️ PumpPortal trader failed - mainnet trading will not work")
            else:
                logger.info("✅ PumpPortal trader initialized successfully (mainnet only)")

        except Exception as e:
            logger.error(f"Error initializing traders: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            # Set both to None to indicate failure
            self.pumpportal_trader = None
            self.helius_trader = None
            
    def set_network_mode(self, network_mode: str):
        """Set network mode - only MAINNET is supported"""
        try:
            if network_mode.upper() != "MAINNET":
                logger.warning(f"Only MAINNET mode is supported. Ignoring request for: {network_mode}")
                return

            self.network_mode = NetworkMode.MAINNET
            self.current_trading_mode = TradingMode.PUMP_FUN

            # Set PumpPortal trader to mainnet mode
            if self.pumpportal_trader:
                self.pumpportal_trader.set_network_mode("MAINNET")
                logger.info("🚀 Confirmed MAINNET mode - using PumpPortal for pump.fun")
            else:
                logger.warning("PumpPortal trader not initialized")

        except ValueError:
            logger.error(f"Invalid network mode: {network_mode}")
            
    def get_current_trader_info(self) -> Dict[str, Any]:
        """Get information about current trading configuration"""
        return {
            "network_mode": self.network_mode.value,
            "trading_mode": self.current_trading_mode.value,
            "active_trader": self._get_active_trader_name(),
            "supports_real_transactions": self._supports_real_transactions()
        }
        
    def _get_active_trader_name(self) -> str:
        """Get name of currently active trader"""
        if self.current_trading_mode == TradingMode.SPL_TOKEN:
            return "Helius SPL Trading"
        elif self.current_trading_mode == TradingMode.PUMP_FUN:
            return "PumpPortal Trading"
        else:
            return "Unknown"

    def _supports_real_transactions(self) -> bool:
        """Check if current mode supports real transactions - always True for mainnet-only bot"""
        return True
        
    async def get_balance(self) -> float:
        """Get balance from appropriate trader"""
        try:
            if self.current_trading_mode == TradingMode.SPL_TOKEN:
                if self.helius_trader:
                    return await self.helius_trader.get_balance()
                else:
                    logger.error("Helius trader not available")
                    return 0.0

            elif self.current_trading_mode == TradingMode.PUMP_FUN:
                if self.pumpportal_trader:
                    return await self.pumpportal_trader.get_balance()
                else:
                    logger.error("PumpPortal trader not available")
                    return 0.0
            else:
                logger.error(f"Unknown trading mode: {self.current_trading_mode}")
                return 0.0

        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return 0.0
            
    async def buy_token(self, token_address: str, sol_amount: float, slippage_percent: float = 10.0, market_data: dict = None) -> Dict[str, Any]:
        """Execute buy order using appropriate trader with adaptive slippage"""
        try:
            # CRITICAL FIX: Calculate adaptive slippage for ALL modes
            adaptive_slippage = self.get_adaptive_slippage(token_address, market_data)

            logger.info(f"🔄 UNIFIED BUY: {sol_amount:.4f} SOL -> {token_address}")
            logger.info(f"📍 Mode: {self.network_mode.value} | Trader: {self._get_active_trader_name()}")
            logger.info(f"🎯 Slippage: {adaptive_slippage:.1f}% (adaptive) vs {slippage_percent:.1f}% (input)")

            if self.current_trading_mode == TradingMode.SPL_TOKEN:
                if self.helius_trader:
                    logger.info(f"🚀 Executing MAINNET SPL token buy via Helius with {adaptive_slippage:.1f}% slippage")
                    return await self.helius_trader.buy_token(token_address, sol_amount, adaptive_slippage)
                else:
                    return self._trader_not_available_error("Helius")

            elif self.current_trading_mode == TradingMode.PUMP_FUN:
                if self.pumpportal_trader:
                    logger.info(f"🚀 Executing MAINNET pump.fun buy via PumpPortal with {adaptive_slippage:.1f}% slippage")
                    return await self.pumpportal_trader.buy_token(token_address, sol_amount, adaptive_slippage)
                else:
                    return self._trader_not_available_error("PumpPortal")
            else:
                logger.error(f"Unknown trading mode: {self.current_trading_mode}")
                return self._trader_not_available_error("Unknown")

        except Exception as e:
            logger.error(f"Error in unified buy: {e}")
            return {
                "success": False,
                "data": {},
                "error": str(e),
                "error_code": "UNIFIED_TRADER_ERROR",
                "retry_after": 10
            }
            
    async def sell_token(self, token_address: str, token_amount: float, slippage_percent: float = 10.0, market_data: dict = None) -> Dict[str, Any]:
        """Execute sell order using appropriate trader with adaptive slippage"""
        try:
            # CRITICAL FIX: Calculate adaptive slippage for ALL modes
            adaptive_slippage = self.get_adaptive_slippage(token_address, market_data)

            logger.info(f"🔄 UNIFIED SELL: {token_amount:.4f} tokens of {token_address}")
            logger.info(f"📍 Mode: {self.network_mode.value} | Trader: {self._get_active_trader_name()}")
            logger.info(f"🎯 Slippage: {adaptive_slippage:.1f}% (adaptive) vs {slippage_percent:.1f}% (input)")

            if self.current_trading_mode == TradingMode.SPL_TOKEN:
                if self.helius_trader:
                    logger.info(f"🚀 Executing MAINNET SPL token sell via Helius with {adaptive_slippage:.1f}% slippage")
                    return await self.helius_trader.sell_token(token_address, token_amount, adaptive_slippage)
                else:
                    return self._trader_not_available_error("Helius")

            elif self.current_trading_mode == TradingMode.PUMP_FUN:
                if self.pumpportal_trader:
                    logger.info(f"🚀 Executing MAINNET pump.fun sell via PumpPortal with {adaptive_slippage:.1f}% slippage")
                    return await self.pumpportal_trader.sell_token(
                        token_address=token_address,
                        token_amount=token_amount,
                        slippage_percent=adaptive_slippage
                    )
                else:
                    return self._trader_not_available_error("PumpPortal")
            else:
                logger.error(f"Unknown trading mode: {self.current_trading_mode}")
                return self._trader_not_available_error("Unknown")

        except Exception as e:
            logger.error(f"Error in unified sell: {e}")
            return {
                "success": False,
                "data": {},
                "error": str(e),
                "error_code": "UNIFIED_TRADER_ERROR",
                "retry_after": 10
            }
            
    def _trader_not_available_error(self, trader_name: str) -> Dict[str, Any]:
        """Return error for unavailable trader"""
        return {
            "success": False,
            "data": {},
            "error": f"{trader_name} trader not available",
            "error_code": "TRADER_NOT_AVAILABLE",
            "retry_after": 0
        }
        
    def get_trading_info(self) -> Dict[str, Any]:
        """Get comprehensive trading information"""
        return {
            "network_mode": self.network_mode.value,
            "trading_mode": self.current_trading_mode.value,
            "active_trader": self._get_active_trader_name(),
            "supports_real_transactions": self._supports_real_transactions(),
            "traders_available": {
                "pumpportal": self.pumpportal_trader is not None,
                "helius": self.helius_trader is not None
            },
            "description": self._get_mode_description()
        }
        
    def _get_mode_description(self) -> str:
        """Get description of current trading mode"""
        if self.current_trading_mode == TradingMode.SPL_TOKEN:
            return "Real mainnet trading with SPL tokens - uses real SOL"
        elif self.current_trading_mode == TradingMode.PUMP_FUN:
            return "Real mainnet trading with pump.fun tokens - uses real SOL"
        else:
            return "Unknown trading mode"
