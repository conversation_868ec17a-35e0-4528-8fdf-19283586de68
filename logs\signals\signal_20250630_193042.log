2025-06-30 19:30:42,651 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-30 19:30:42,651 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-30 19:30:42,653 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\final 2nd\session_string.json (length: 353)
2025-06-30 19:30:42,654 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_fresh_1751292042
2025-06-30 19:30:42,654 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\final 2nd\trading_real_session_fresh_1751292042
2025-06-30 19:30:42,655 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-30 19:30:42,671 - signal_handler - INFO - Direct signal callback registered
2025-06-30 19:30:42,675 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-30 19:30:42,675 - signal_handler - INFO - API ID loaded: True
2025-06-30 19:30:42,675 - signal_handler - INFO - API Hash loaded: True
2025-06-30 19:30:42,676 - signal_handler - INFO - Phone number loaded: True
2025-06-30 19:30:42,676 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-30 19:30:42,676 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-30 19:30:42,676 - signal_handler - INFO - Using saved session string
2025-06-30 19:30:42,677 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-30 19:30:42,992 - signal_handler - INFO - Checking authorization status...
2025-06-30 19:30:43,084 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-30 19:30:43,085 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-30 19:30:43,086 - signal_handler - INFO - Checking authorization status...
2025-06-30 19:30:43,143 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-30 19:30:43,144 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-30 19:30:43,145 - signal_handler - INFO - Checking authorization status...
2025-06-30 19:30:43,201 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-30 19:30:43,202 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-30 19:30:43,203 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-30 19:30:43,204 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-30 19:30:43,402 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-30 19:30:43,404 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-30 19:30:43,405 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-30 19:30:43,406 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-30 19:30:43,416 - signal_handler - INFO - Direct signal callback registered
2025-06-30 19:30:44,812 - signal_handler - INFO - Signal listener running (attempt 1/3).
