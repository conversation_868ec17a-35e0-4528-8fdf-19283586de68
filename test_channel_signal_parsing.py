#!/usr/bin/env python3
"""
Comprehensive test for channel signal parsing and token extraction.
Tests all configured channels with sample messages to verify proper token detection.
"""

import re
import json
import asyncio
import sys
import os
from typing import Dict, List, Optional, Any

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the signal handler and related modules
try:
    from signal_handler import SignalHandler
    from config_manager import ConfigManager
    print("✅ Successfully imported signal handler modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    print("Make sure you're running this from the bot directory")
    sys.exit(1)

class ChannelSignalTester:
    def __init__(self):
        """Initialize the tester with sample messages from each channel"""
        
        # Load configuration to get channel list
        try:
            with open('finalconfig.json', 'r') as f:
                config_data = json.load(f)
            self.target_channels = config_data.get('telegram_settings', {}).get('target_channels', [])
            print(f"✅ Loaded {len(self.target_channels)} target channels from config")
        except Exception as e:
            print(f"❌ Failed to load config: {e}")
            sys.exit(1)
        
        # Channel information mapping
        self.channel_info = {
            -1001509251052: {"name": "Solana Gems Radar", "type": "gems"},
            -1002187518673: {"name": "DONALD CALLS", "type": "calls"},
            -1002017173747: {"name": "SOL HIGH VOLUME ALERT | Bordga", "type": "volume_alert"},
            -1002017857449: {"name": "PEPE CALLS", "type": "calls"},
            -1002177594166: {"name": "OxyZen Calls", "type": "calls"},
            -1002093384030: {"name": "Solana Early Trending", "type": "early_trending"},
            -1002428819353: {"name": "Solbix Community Calls", "type": "community_calls"}
        }
        
        # Sample messages for each channel type
        self.sample_messages = {
            # SOLBIX Community Calls - Your actual message
            -1002428819353: [
                {
                    "text": """Call by 𝕏 @vsagarp1010 (https://x.com/vsagarp1010)   
Total Calls: 32 (https://t.me/solbix_bot?start=1819913046)
Total X: 64.0X
Average X per call:  2.1X
Caller's 𝕏: @vsagarp1010 (https://x.com/vsagarp1010)
       

🪙 USELESS HOUSE ($UHOUSE)
└7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump

🏦 Market Cap: $36,396
🛠 Created On: PumpFun (https://pump.fun/coin/7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump)
🦅 DexS Paid?: ✅""",
                    "expected_token": "7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump",
                    "description": "SOLBIX UHOUSE signal with └ symbol format"
                }
            ],
            
            # OxyZen Calls - Based on successful signal from logs
            -1002177594166: [
                {
                    "text": """🔥 NEW CALL 🔥

$TESTCOIN (TESTCOIN)
CA: Ce7LSREbs4Z4TMyzSAb8bxe3p52btWfxHgRdcsyJpump

💎 Market Cap: $45K
🔥 Liquidity: $12K SOL
⚡ Volume 24h: $89K

🎯 Target: 5-10x
⏰ Time: NOW""",
                    "expected_token": "Ce7LSREbs4Z4TMyzSAb8bxe3p52btWfxHgRdcsyJpump",
                    "description": "OxyZen call with CA: format"
                }
            ],
            
            # Solana Early Trending - Soul Sniper format
            -1002093384030: [
                {
                    "text": """🚀 EARLY TRENDING ALERT 🚀

New token detected with high volume!
Check it out: https://t.me/soul_sniper_bot?start=15_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump

Early entry opportunity - DYOR!""",
                    "expected_token": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "Early trending with soul_sniper_bot link (CORRECT FORMAT)"
                },
                {
                    "text": """🚀 EARLY TRENDING ALERT 🚀

New token detected with high volume!
Check it out: https://t.me/soul_sniper_bot?start=123_9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump

Early entry opportunity - DYOR!""",
                    "expected_token": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "Early trending with soul_sniper_bot link (WRONG PREFIX - should fail)"
                },
                {
                    "text": """🚀 EARLY TRENDING ALERT 🚀

$NEWTOKEN (NEW TOKEN)
CA: 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump

Market Cap: $45K
Early entry opportunity - DYOR!""",
                    "expected_token": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "Early trending with CA: format (should fail - no soul_sniper link)"
                },
                {
                    "text": """📈 [**NEWCOIN**](https://www.geckoterminal.com/solana/pools/9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump)

🔥 Early trending token detected!
Check DexScreener: https://dexscreener.com/solana/9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump""",
                    "expected_token": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "Early trending with markdown link format (should fail - no soul_sniper link)"
                }
            ],
            
            # DONALD CALLS
            -1002187518673: [
                {
                    "text": """🦆 DONALD CALL 🦆

$DUCK (DUCK TOKEN)
Contract: 8WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump

MC: $67K | Liq: $23K
SEND IT! 🚀""",
                    "expected_token": "8WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "DONALD call with Contract: format"
                }
            ],
            
            # PEPE CALLS
            -1002017857449: [
                {
                    "text": """🐸 PEPE CALL 🐸

$FROG (FROG COIN)
Token Address: 7WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump

Market Cap: $89K
Liquidity: $34K SOL
PEPE APPROVED! 🐸✅""",
                    "expected_token": "7WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "PEPE call with Token Address: format"
                }
            ],
            
            # SOL HIGH VOLUME ALERT
            -1002017173747: [
                {
                    "text": """🔊 HIGH VOLUME ALERT 🔊

Volume spike detected!
Token: 6WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump
24h Volume: $456K (+2,340%)

Check DexScreener: https://dexscreener.com/solana/6WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump""",
                    "expected_token": "6WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "Volume alert with direct token and DexScreener URL"
                }
            ],
            
            # Solana Gems Radar
            -1001509251052: [
                {
                    "text": """💎 GEM RADAR 💎

New gem detected: $GEMCOIN
CA: 5WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump

✅ Verified contract
✅ Liquidity locked
✅ Low market cap: $23K

Gem score: 9.2/10 💎""",
                    "expected_token": "5WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",
                    "description": "Gems radar with CA: format"
                }
            ]
        }
        
        # Initialize signal handler for testing
        try:
            config_manager = ConfigManager()
            self.signal_handler = SignalHandler(config_manager)
            print("✅ Signal handler initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize signal handler: {e}")
            sys.exit(1)

    def test_regex_patterns(self):
        """Test the core regex patterns used for token extraction"""
        print("\n" + "="*60)
        print("TESTING CORE REGEX PATTERNS")
        print("="*60)
        
        test_tokens = [
            "7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump",  # SOLBIX token
            "Ce7LSREbs4Z4TMyzSAb8bxe3p52btWfxHgRdcsyJpump",  # OxyZen token
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bkpump",   # Test pump token
            "8WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9Bk",        # Non-pump token
        ]
        
        patterns_to_test = [
            (r'([1-9A-HJ-NP-Za-km-z]{32,44}pump)', "Pump.fun pattern"),
            (r'([1-9A-HJ-NP-Za-km-z]{32,44})', "General Solana address"),
            (r'(?:CA|ca|Contract|TOKEN)\s*:?\s*([1-9A-HJ-NP-Za-km-z]{32,44})', "CA pattern"),
            (r'Token Address:?\s*([1-9A-HJ-NP-Za-km-z]{32,44})', "Token Address pattern"),
            (r'└([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)', "└ symbol pattern"),
        ]
        
        for token in test_tokens:
            print(f"\nTesting token: {token}")
            for pattern, description in patterns_to_test:
                match = re.search(pattern, token)
                if match:
                    print(f"  ✅ {description}: {match.group(1)}")
                else:
                    print(f"  ❌ {description}: No match")

    def test_channel_message_parsing(self):
        """Test message parsing for each channel"""
        print("\n" + "="*60)
        print("TESTING CHANNEL MESSAGE PARSING")
        print("="*60)
        
        results = {}
        
        for channel_id, messages in self.sample_messages.items():
            channel_name = self.channel_info.get(channel_id, {}).get("name", f"Channel {channel_id}")
            print(f"\n🔍 Testing {channel_name} (ID: {channel_id})")
            print("-" * 50)
            
            channel_results = []
            
            for i, msg_data in enumerate(messages):
                text = msg_data["text"]
                expected_token = msg_data["expected_token"]
                description = msg_data["description"]
                
                print(f"\nTest {i+1}: {description}")
                print(f"Expected token: {expected_token}")
                
                # Test with different extraction methods
                extraction_results = {}
                
                # Method 1: _extract_token_addresses
                try:
                    addresses = self.signal_handler._extract_token_addresses(text, channel_id)
                    extraction_results["_extract_token_addresses"] = addresses[0] if addresses else None
                except Exception as e:
                    extraction_results["_extract_token_addresses"] = f"ERROR: {e}"
                
                # Method 2: extract_ca_id_only
                try:
                    channel_info = {'channel_id': channel_id}
                    token = self.signal_handler.extract_ca_id_only(text, channel_info)
                    extraction_results["extract_ca_id_only"] = token
                except Exception as e:
                    extraction_results["extract_ca_id_only"] = f"ERROR: {e}"
                
                # Method 3: extract_token_address
                try:
                    channel_info = {'channel_id': channel_id}
                    token = self.signal_handler.extract_token_address(text, channel_info)
                    extraction_results["extract_token_address"] = token
                except Exception as e:
                    extraction_results["extract_token_address"] = f"ERROR: {e}"
                
                # Analyze results
                success_count = 0
                for method, result in extraction_results.items():
                    if result == expected_token:
                        print(f"  ✅ {method}: {result}")
                        success_count += 1
                    elif result and not result.startswith("ERROR"):
                        print(f"  ⚠️  {method}: {result} (WRONG)")
                    elif result and result.startswith("ERROR"):
                        print(f"  ❌ {method}: {result}")
                    else:
                        print(f"  ❌ {method}: No token found")
                
                test_result = {
                    "description": description,
                    "expected": expected_token,
                    "results": extraction_results,
                    "success_count": success_count,
                    "total_methods": len(extraction_results)
                }
                channel_results.append(test_result)
            
            results[channel_id] = {
                "name": channel_name,
                "tests": channel_results
            }
        
        return results

    async def test_full_signal_extraction(self):
        """Test the complete signal extraction pipeline"""
        print("\n" + "="*60)
        print("TESTING FULL SIGNAL EXTRACTION PIPELINE")
        print("="*60)
        
        for channel_id, messages in self.sample_messages.items():
            channel_name = self.channel_info.get(channel_id, {}).get("name", f"Channel {channel_id}")
            print(f"\n🔄 Testing full pipeline for {channel_name}")
            print("-" * 50)
            
            for i, msg_data in enumerate(messages):
                text = msg_data["text"]
                expected_token = msg_data["expected_token"]
                description = msg_data["description"]
                
                print(f"\nTest {i+1}: {description}")
                
                # Create message object as the signal handler expects
                message_obj = {
                    'text': text,
                    'chat_id': channel_id,
                    'date': None,
                    'is_gmgn_channel': False,
                    'channel_identifier': None
                }
                
                try:
                    # Test the full _extract_signal method
                    signal = await self.signal_handler._extract_signal(message_obj)
                    
                    if signal:
                        extracted_token = signal.get('token_address') or signal.get('token')
                        source_type = signal.get('source_type')
                        
                        if extracted_token == expected_token:
                            print(f"  ✅ Full pipeline SUCCESS")
                            print(f"     Token: {extracted_token}")
                            print(f"     Source: {source_type}")
                            print(f"     Signal keys: {list(signal.keys())}")
                        else:
                            print(f"  ⚠️  Full pipeline PARTIAL SUCCESS")
                            print(f"     Expected: {expected_token}")
                            print(f"     Got: {extracted_token}")
                            print(f"     Source: {source_type}")
                    else:
                        print(f"  ❌ Full pipeline FAILED - No signal extracted")
                        
                except Exception as e:
                    print(f"  ❌ Full pipeline ERROR: {e}")

    def generate_summary_report(self, results):
        """Generate a summary report of all tests"""
        print("\n" + "="*60)
        print("SUMMARY REPORT")
        print("="*60)
        
        total_channels = len(results)
        total_tests = sum(len(data["tests"]) for data in results.values())
        successful_channels = 0
        
        for channel_id, data in results.items():
            channel_name = data["name"]
            tests = data["tests"]
            
            # Calculate success rate for this channel
            total_success = sum(test["success_count"] for test in tests)
            total_possible = sum(test["total_methods"] for test in tests)
            success_rate = (total_success / total_possible * 100) if total_possible > 0 else 0
            
            status = "✅" if success_rate >= 66 else "⚠️" if success_rate >= 33 else "❌"
            print(f"{status} {channel_name}: {success_rate:.1f}% success rate ({total_success}/{total_possible})")
            
            if success_rate >= 66:
                successful_channels += 1
        
        print(f"\nOverall Results:")
        print(f"  Channels tested: {total_channels}")
        print(f"  Channels working well (≥66%): {successful_channels}")
        print(f"  Total test cases: {total_tests}")
        
        # Recommendations
        print(f"\nRecommendations:")
        failing_channels = [data["name"] for channel_id, data in results.items() 
                          if sum(test["success_count"] for test in data["tests"]) / 
                             sum(test["total_methods"] for test in data["tests"]) < 0.66]
        
        if failing_channels:
            print(f"  ⚠️  Review token extraction for: {', '.join(failing_channels)}")
        else:
            print(f"  ✅ All channels are extracting tokens properly!")

    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 COMPREHENSIVE CHANNEL SIGNAL PARSING TEST")
        print("=" * 60)
        print(f"Testing {len(self.target_channels)} configured channels")
        print(f"Sample messages prepared for {len(self.sample_messages)} channels")
        
        # Test 1: Regex patterns
        self.test_regex_patterns()
        
        # Test 2: Channel message parsing
        results = self.test_channel_message_parsing()
        
        # Test 3: Full signal extraction pipeline
        await self.test_full_signal_extraction()
        
        # Test 4: Generate summary
        self.generate_summary_report(results)
        
        print(f"\n🏁 Testing completed!")

async def main():
    """Main test function"""
    tester = ChannelSignalTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
