#!/usr/bin/env python3
"""
COMPREHENSIVE BUY/SELL TEST - DIRECT EXECUTION
Tests buy and sell operations with specific tokens using 0.001 SOL
"""

import asyncio
import logging
import time
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test tokens as requested
TEST_TOKENS = [
    "HYUZghyXCimMEQqLRo7tmRbtjyrPEqkM9VQsFcWKpump",
    "zByW1uVMmEmGpbr1yAgF9nxeDd6NKMRDbautoLwpump", 
    "GkR6xgN7qfFxGdFBbj7hwT7NpwT7YkF4neXQeYLSpump"
]

TEST_AMOUNT_SOL = 0.001  # As requested

async def test_token_buy_sell(token_address: str, trader) -> dict:
    """Test buy and sell operations for a single token"""
    logger.info(f"=== TESTING TOKEN: {token_address[:8]}... ===")
    
    result = {
        "token": token_address,
        "timestamp": datetime.now().isoformat(),
        "buy_test": {},
        "sell_test": {},
        "overall_success": False
    }
    
    try:
        # Get initial balance
        initial_balance = await trader.get_balance()
        logger.info(f"Initial SOL balance: {initial_balance:.6f}")
        
        # TEST 1: BUY OPERATION
        logger.info(f"TESTING BUY: {TEST_AMOUNT_SOL} SOL worth of {token_address[:8]}...")
        
        buy_start = time.time()
        buy_result = await trader.buy_token(
            token_address=token_address,
            sol_amount=TEST_AMOUNT_SOL,
            slippage_percent=1.0  # 1% slippage
        )
        buy_time = time.time() - buy_start
        
        if buy_result and buy_result.get('success', False):
            # Extract transaction signature from multiple possible locations
            tx_sig = (buy_result.get('transaction_signature') or
                     buy_result.get('data', {}).get('transaction_signature') or
                     buy_result.get('tx_signature', 'N/A'))

            # Extract tokens received (may not be available in buy result)
            tokens_received = buy_result.get('data', {}).get('tokens_received', 0)

            # ALWAYS check actual wallet balance regardless of buy result
            logger.info(f"🔍 Checking actual wallet balance for {token_address[:8]}...")
            try:
                # Get wallet public key
                import base58
                from solders.keypair import Keypair

                wallet_info = trader.default_wallet
                private_key_bytes = base58.b58decode(wallet_info['private_key'])
                if len(private_key_bytes) == 64:
                    keypair = Keypair.from_bytes(private_key_bytes)
                elif len(private_key_bytes) == 32:
                    keypair = Keypair.from_seed(private_key_bytes)
                else:
                    keypair = Keypair.from_bytes(private_key_bytes)

                public_key = str(keypair.pubkey())

                # Check actual token balance
                actual_balance = await trader._get_token_balance(public_key, token_address, retry_count=3)
                if actual_balance and actual_balance > 0:
                    tokens_received = actual_balance
                    logger.info(f"✅ Found {tokens_received:,.6f} tokens in wallet - PROCEEDING WITH SELL TEST")
                else:
                    logger.warning(f"⚠️ No tokens found in wallet - cannot test sell")

            except Exception as e:
                logger.error(f"❌ Error checking wallet balance: {e}")

            logger.info(f"✅ BUY SUCCESS: {tokens_received:,.6f} tokens received")
            logger.info(f"   Transaction: {tx_sig}")
            logger.info(f"   Execution time: {buy_time:.2f}s")
            
            result["buy_test"] = {
                "success": True,
                "tokens_received": tokens_received,
                "transaction_signature": tx_sig,
                "execution_time": buy_time,
                "verified": buy_result.get('verified', False)
            }
            
            # Wait a moment for blockchain confirmation
            await asyncio.sleep(3)
            
            # TEST 2: SELL OPERATION (only if buy succeeded)
            if tokens_received > 0:
                logger.info(f"TESTING SELL: {tokens_received:,.0f} tokens...")
                
                sell_start = time.time()
                sell_result = await trader.sell_token(
                    token_address=token_address,
                    token_amount=tokens_received,
                    slippage_percent=1.0  # 1% slippage
                )
                sell_time = time.time() - sell_start
                
                if sell_result and sell_result.get('success', False):
                    sol_received = sell_result.get('data', {}).get('sol_received', 0)
                    sell_tx_sig = sell_result.get('transaction_signature', 'N/A')
                    
                    logger.info(f"✅ SELL SUCCESS: {sol_received:.6f} SOL received")
                    logger.info(f"   Transaction: {sell_tx_sig}")
                    logger.info(f"   Execution time: {sell_time:.2f}s")
                    
                    # BULLETPROOF VERIFICATION: Check if sell actually happened
                    await asyncio.sleep(3)
                    final_balance = await trader.get_balance()
                    net_change = final_balance - initial_balance
                    
                    logger.info(f"VERIFICATION:")
                    logger.info(f"   Initial balance: {initial_balance:.6f} SOL")
                    logger.info(f"   Final balance: {final_balance:.6f} SOL")
                    logger.info(f"   Net change: {net_change:.6f} SOL")
                    
                    # Check for phantom sell
                    phantom_sell = (sell_result.get('success') and sol_received > 0 and net_change <= -TEST_AMOUNT_SOL)
                    
                    result["sell_test"] = {
                        "success": True,
                        "sol_received": sol_received,
                        "transaction_signature": sell_tx_sig,
                        "execution_time": sell_time,
                        "verified": sell_result.get('verified', False),
                        "net_change": net_change,
                        "phantom_sell": phantom_sell
                    }
                    
                    if phantom_sell:
                        logger.error(f"🚫 PHANTOM SELL DETECTED!")
                        logger.error(f"🚫 Sell claimed success but balance decreased by full amount")
                    else:
                        logger.info(f"✅ SELL VERIFIED: Actual balance change confirms sell")
                    
                    result["overall_success"] = True
                    
                else:
                    error = sell_result.get('error', 'Unknown error') if sell_result else 'No result'
                    logger.error(f"❌ SELL FAILED: {error}")
                    result["sell_test"] = {"success": False, "error": error}
            else:
                logger.error("❌ SKIPPING SELL: No tokens received from buy")
                result["sell_test"] = {"success": False, "error": "No tokens to sell"}
        else:
            error = buy_result.get('error', 'Unknown error') if buy_result else 'No result'
            logger.error(f"❌ BUY FAILED: {error}")
            result["buy_test"] = {"success": False, "error": error}
            result["sell_test"] = {"success": False, "error": "Buy failed, cannot test sell"}
    
    except Exception as e:
        logger.error(f"❌ TEST FAILED: {e}")
        result["error"] = str(e)
    
    logger.info(f"=== COMPLETED TOKEN: {token_address[:8]}... ===\n")
    return result

async def run_comprehensive_tests():
    """Run comprehensive buy/sell tests for all tokens"""
    logger.info("🚀 STARTING COMPREHENSIVE BUY/SELL TESTS")
    logger.info("=" * 80)
    logger.info(f"Testing {len(TEST_TOKENS)} tokens with {TEST_AMOUNT_SOL} SOL each")
    logger.info("=" * 80)
    
    # Initialize trader with config
    try:
        from pumpportal_trader import PumpPortalTrader
        from config_manager import ConfigManager

        # Load config
        config_manager = ConfigManager()

        # Initialize trader
        trader = PumpPortalTrader(config_manager)
        logger.info("✅ Trader initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize trader: {e}")
        return
    
    # Run tests for each token
    all_results = {
        "test_suite": "Comprehensive Buy/Sell Tests",
        "timestamp": datetime.now().isoformat(),
        "test_amount_sol": TEST_AMOUNT_SOL,
        "tokens_tested": len(TEST_TOKENS),
        "results": []
    }
    
    for i, token_address in enumerate(TEST_TOKENS, 1):
        logger.info(f"🔄 TESTING TOKEN {i}/{len(TEST_TOKENS)}")
        
        try:
            result = await test_token_buy_sell(token_address, trader)
            all_results["results"].append(result)
            
            # Wait between tests
            if i < len(TEST_TOKENS):
                logger.info("⏳ Waiting 10 seconds before next test...")
                await asyncio.sleep(10)
                
        except Exception as e:
            logger.error(f"❌ Test failed for {token_address[:8]}: {e}")
            all_results["results"].append({
                "token": token_address,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "overall_success": False
            })
    
    # Calculate statistics
    successful_tests = sum(1 for r in all_results["results"] if r.get("overall_success", False))
    phantom_sells = sum(1 for r in all_results["results"] 
                       if r.get("sell_test", {}).get("phantom_sell", False))
    
    all_results["successful_tests"] = successful_tests
    all_results["phantom_sells_detected"] = phantom_sells
    all_results["success_rate"] = successful_tests / len(TEST_TOKENS) if TEST_TOKENS else 0
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"buy_sell_test_results_{timestamp}.json"
    
    try:
        with open(filename, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        logger.info(f"💾 Results saved to {filename}")
    except Exception as e:
        logger.error(f"❌ Failed to save results: {e}")
    
    # Print final summary
    logger.info("🏁 COMPREHENSIVE TESTS COMPLETED!")
    logger.info("=" * 80)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   Success Rate: {successful_tests}/{len(TEST_TOKENS)} ({all_results['success_rate']:.1%})")
    logger.info(f"   Phantom Sells: {phantom_sells}")
    
    if phantom_sells == 0:
        logger.info("🛡️ BULLETPROOF SYSTEM: ✅ NO PHANTOM SELLS DETECTED!")
    else:
        logger.error(f"⚠️ SYSTEM ISSUES: {phantom_sells} phantom sells detected")
    
    logger.info("=" * 80)
    
    return all_results

if __name__ == "__main__":
    asyncio.run(run_comprehensive_tests())
