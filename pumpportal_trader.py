#!/usr/bin/env python3
"""
PumpPortal Trader Integration for Real Mode
Replaces GMGN trader with direct Solana transactions via Helius RPC
"""

import logging
import os
import sys
import base64
import asyncio
import time
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from helius_rate_limiter import get_helius_rate_limiter

# Import Solana packages
try:
    import requests
    import base58
    from solders.keypair import Keypair
    from solders.transaction import VersionedTransaction
    from solders import message
except ImportError as e:
    print(f"❌ Missing packages: {e}")
    print("💡 Install: pip install solders solana requests base58")
    sys.exit(1)

logger = logging.getLogger(__name__)

class PumpPortalTrader:
    """PumpPortal trader for real mode execution - replaces GMGN trader"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager

        # CRITICAL FIX: Set network mode BEFORE calling _get_helius_rpc_url()
        self.current_network_mode = "MAINNET"  # Default to mainnet

        # Get API settings
        api_endpoints = config_manager.get_section('api_endpoints')
        self.pumpportal_api_url = api_endpoints.get('pumpportal_api', 'https://pumpportal.fun/api/trade-local')
        # CRITICAL FIX: PumpPortal is MAINNET ONLY - no devnet API exists
        # PumpPortal is mainnet-only
        self.helius_rpc_url = self._get_helius_rpc_url()

        # Initialize Helius rate limiter
        self.helius_limiter = get_helius_rate_limiter(config_manager)

        # SURGICAL FIX: Load transaction settings for external fees from root level
        self.transaction_settings = config_manager.get_section('transaction_settings')
        logger.info(f"Loaded transaction settings: {self.transaction_settings}")

        # Load default wallet
        self.default_wallet = self._load_default_wallet()

        # Transaction hash cache for recent sells (to fix ALREADY_SOLD_ZERO_BALANCE issue)
        self.recent_sell_transactions = {}  # {token_address: {'tx_hash': str, 'timestamp': float}}

        logger.info("PumpPortal Trader initialized for mainnet pump.fun trading")
        logger.info(f"Mainnet API URL: {self.pumpportal_api_url}")
        logger.info("⚠️ Note: PumpPortal only supports mainnet")
        logger.info(f"RPC URL: {self.helius_rpc_url[:50]}...")
        logger.info(f"Helius rate limiter: {self.helius_limiter.config.requests_per_second} RPS")

    def set_network_mode(self, network_mode: str):
        """Set the network mode (MAINNET ONLY - devnet disabled)"""
        self.current_network_mode = "MAINNET"  # Force mainnet only

        # Update RPC URL to mainnet
        self.helius_rpc_url = self._get_helius_rpc_url()

        logger.info(f"🔄 PumpPortal trader set to MAINNET mode")
        logger.info(f"🌐 Updated RPC URL: {self.helius_rpc_url[:50]}...")
        logger.info(f"🔗 API URL: {self.get_current_api_url()}")

    def _cleanup_transaction_cache(self):
        """Clean up old cached transactions to prevent memory bloat"""
        try:
            current_time = time.time()
            expired_tokens = []

            for token_address, tx_data in self.recent_sell_transactions.items():
                if current_time - tx_data['timestamp'] > 3600:  # 1 hour
                    expired_tokens.append(token_address)

            for token_address in expired_tokens:
                del self.recent_sell_transactions[token_address]

            if expired_tokens:
                logger.debug(f"🧹 Cleaned up {len(expired_tokens)} expired transaction cache entries")
        except Exception as e:
            logger.error(f"Error cleaning transaction cache: {e}")

    def get_current_api_url(self) -> str:
        """Get the PumpPortal API URL (MAINNET ONLY - devnet disabled)"""
        # Always return mainnet PumpPortal API
        return self.pumpportal_api_url

    def _get_helius_rpc_url(self) -> str:
        """Get Helius RPC URL with API key - network aware"""
        try:
            api_endpoints = self.config_manager.get_section('api_endpoints')

            # MAINNET ONLY: Always use mainnet RPC (devnet disabled)
            helius_template = api_endpoints.get('helius_rpc_mainnet', 'https://mainnet.helius-rpc.com/?api-key={api_key}')
            fallback_rpc = 'https://api.mainnet-beta.solana.com'

            # Get API key from .env
            load_dotenv(override=True)
            api_key = os.getenv('HELIUS_API_KEY')

            if not api_key:
                logger.warning(f"HELIUS_API_KEY not found in .env, using default mainnet RPC")
                return fallback_rpc

            rpc_url = helius_template.format(api_key=api_key)
            logger.debug(f"Using MAINNET RPC: {rpc_url[:50]}...")
            return rpc_url

        except Exception as e:
            logger.error(f"Error getting Helius RPC URL: {e}")
            return 'https://api.mainnet-beta.solana.com'
    
    def _load_default_wallet(self) -> Optional[Dict[str, Any]]:
        """Load default wallet from .env based on config setting with public key derivation"""
        try:
            load_dotenv(override=True)

            # Get preferred wallet name from config
            wallet_settings = self.config_manager.get_section('wallet_settings')
            preferred_wallet = wallet_settings.get('default_wallet_name', '').upper()

            # First try to load the preferred wallet
            if preferred_wallet:
                private_key = os.getenv(f'WALLET_{preferred_wallet}_PRIVATE_KEY')
                if private_key:
                    # CRITICAL FIX: Derive public key from private key
                    try:
                        private_key_bytes = base58.b58decode(private_key)
                        if len(private_key_bytes) == 64:
                            keypair = Keypair.from_bytes(private_key_bytes)
                        elif len(private_key_bytes) == 32:
                            keypair = Keypair.from_seed(private_key_bytes)
                        else:
                            keypair = Keypair.from_bytes(private_key_bytes)

                        public_key = str(keypair.pubkey())
                        logger.info(f"Loaded configured default wallet: {preferred_wallet} with public key: {public_key}")
                        return {
                            'name': preferred_wallet,
                            'private_key': private_key,
                            'public_key': public_key
                        }
                    except Exception as e:
                        logger.error(f"Failed to derive public key for wallet {preferred_wallet}: {e}")
                        return None
                else:
                    logger.warning(f"Configured default wallet '{preferred_wallet}' not found in .env")

            # Fallback: find any wallet in .env
            for key in os.environ:
                if key.startswith('WALLET_') and key.endswith('_PRIVATE_KEY'):
                    wallet_name = key.replace('WALLET_', '').replace('_PRIVATE_KEY', '')
                    private_key = os.getenv(key)

                    if private_key:
                        # CRITICAL FIX: Derive public key for fallback wallet too
                        try:
                            private_key_bytes = base58.b58decode(private_key)
                            if len(private_key_bytes) == 64:
                                keypair = Keypair.from_bytes(private_key_bytes)
                            elif len(private_key_bytes) == 32:
                                keypair = Keypair.from_seed(private_key_bytes)
                            else:
                                keypair = Keypair.from_bytes(private_key_bytes)

                            public_key = str(keypair.pubkey())
                            logger.info(f"Using fallback wallet: {wallet_name} with public key: {public_key}")
                            return {
                                'name': wallet_name,
                                'private_key': private_key,
                                'public_key': public_key
                            }
                        except Exception as e:
                            logger.error(f"Failed to derive public key for fallback wallet {wallet_name}: {e}")
                            continue

            logger.warning("No wallet found in .env file")
            return None

        except Exception as e:
            logger.error(f"Error loading default wallet: {e}")
            return None
    
    def get_wallet_from_env(self, wallet_name: str) -> Optional[Dict[str, Any]]:
        """Get specific wallet from .env file with public key derivation"""
        try:
            load_dotenv(override=True)
            private_key = os.getenv(f'WALLET_{wallet_name.upper()}_PRIVATE_KEY')

            if not private_key:
                return None

            # CRITICAL FIX: Derive public key from private key
            try:
                private_key_bytes = base58.b58decode(private_key)
                if len(private_key_bytes) == 64:
                    keypair = Keypair.from_bytes(private_key_bytes)
                elif len(private_key_bytes) == 32:
                    keypair = Keypair.from_seed(private_key_bytes)
                else:
                    keypair = Keypair.from_bytes(private_key_bytes)

                public_key = str(keypair.pubkey())
                return {
                    'name': wallet_name,
                    'private_key': private_key,
                    'public_key': public_key
                }
            except Exception as e:
                logger.error(f"Failed to derive public key for wallet {wallet_name}: {e}")
                return None

        except Exception as e:
            logger.error(f"Error loading wallet {wallet_name}: {e}")
            return None
    
    async def check_wallet_balance(self) -> float:
        """Check current SOL balance of the default wallet"""
        try:
            if not self.default_wallet:
                logger.error("No wallet available for balance check")
                return 0.0

            # Get public key from wallet
            private_key_bytes = base58.b58decode(self.default_wallet['private_key'])
            if len(private_key_bytes) == 64:
                keypair = Keypair.from_bytes(private_key_bytes)
            elif len(private_key_bytes) == 32:
                keypair = Keypair.from_seed(private_key_bytes)
            else:
                keypair = Keypair.from_bytes(private_key_bytes)
            public_key = str(keypair.pubkey())

            # Prepare RPC request for balance
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [public_key]
            }

            # Use Helius rate limiter for balance check
            response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, payload, timeout=6)

            if response_data and 'result' in response_data and 'value' in response_data['result']:
                lamports = response_data['result']['value']
                sol_balance = lamports / 1_000_000_000  # Convert lamports to SOL
                logger.debug(f"Wallet balance: {sol_balance:.6f} SOL")
                return sol_balance
            else:
                logger.error("Failed to get wallet balance from RPC")
                return 0.0

        except Exception as e:
            logger.error(f"Error checking wallet balance: {e}")
            return 0.0

    async def buy_token(self, token_address: str, sol_amount: float, slippage_percent: float = 10.0) -> Dict[str, Any]:
        """Execute buy order via PumpPortal with balance validation and external fees"""
        try:
            if not self.default_wallet:
                logger.error("No wallet available for buy order")
                return {
                    "success": False,
                    "data": {},
                    "error": "No wallet available for buy order",
                    "error_code": "NO_WALLET",
                    "retry_after": 0
                }

            # CALCULATE EXTERNAL FEES FROM CONFIG (FIXED: Reasonable defaults)
            base_buy_tip = self.transaction_settings.get('buy_tip_sol', 0.0001)
            base_gas_price = self.transaction_settings.get('gas_price_sol', 0.0001)

            # Apply dynamic priority fees
            buy_tip = await self._calculate_dynamic_priority_fee(base_buy_tip)
            gas_price = await self._calculate_dynamic_priority_fee(base_gas_price)
            handling_fee_percent = self.transaction_settings.get('handling_fee_percent', 0.0)  # Fixed: No handling fee
            platform_fee_percent = self.transaction_settings.get('platform_fee_percent', 0.5)  # PumpPortal fee
            pumpfun_fee_percent = self.transaction_settings.get('pumpfun_fee_percent', 1.0)  # Pump.fun bonding curve fee

            # Calculate percentage-based fees
            handling_fee_sol = sol_amount * (handling_fee_percent / 100.0)
            platform_fee_sol = sol_amount * (platform_fee_percent / 100.0)
            pumpfun_fee_sol = sol_amount * (pumpfun_fee_percent / 100.0)

            # Total external fees
            total_external_fees = buy_tip + gas_price + handling_fee_sol + platform_fee_sol + pumpfun_fee_sol

            logger.info(f"💰 EXTERNAL FEES: Buy tip: {buy_tip:.4f} SOL, Gas: {gas_price:.4f} SOL, Handling: {handling_fee_sol:.4f} SOL, Platform: {platform_fee_sol:.4f} SOL")
            logger.info(f"💰 TOTAL EXTERNAL FEES: {total_external_fees:.4f} SOL")

            # BALANCE VALIDATION - Check wallet balance including external fees
            current_balance = await self.check_wallet_balance()
            total_required = sol_amount + total_external_fees

            if current_balance < total_required:
                error_msg = f"Insufficient balance: Need {total_required:.4f} SOL (trade: {sol_amount:.4f} + fees: {total_external_fees:.4f}), have {current_balance:.4f} SOL"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "data": {"required": total_required, "available": current_balance},
                    "error": error_msg,
                    "error_code": "INSUFFICIENT_BALANCE",
                    "retry_after": 0
                }

            if current_balance < 0.05:  # Warn when balance is getting low
                logger.warning(f"⚠️ Low wallet balance: {current_balance:.4f} SOL remaining")

            # Convert slippage from percentage to integer
            slippage_int = int(slippage_percent)

            logger.info(f"🟢 REAL BUY: {sol_amount:.4f} SOL of {token_address} (slippage: {slippage_percent}%) + {total_external_fees:.4f} SOL fees - Balance: {current_balance:.4f} SOL")

            result = await self._execute_trade(
                wallet=self.default_wallet,
                action="buy",
                mint=token_address,
                amount=sol_amount,
                denominated_in_sol=True,
                slippage=slippage_int,
                external_fees=total_external_fees
            )

            if result["success"]:
                logger.info(f"✅ Buy successful: {result['transaction_signature']} (Total cost: {sol_amount + total_external_fees:.4f} SOL)")
                return {
                    "success": True,
                    "data": {
                        "transaction_signature": result['transaction_signature'],
                        "sol_amount": sol_amount,
                        "total_cost": sol_amount + total_external_fees,
                        "external_fees": total_external_fees
                    },
                    "error": "",
                    "error_code": "",
                    "retry_after": 0
                }
            else:
                logger.error(f"❌ Buy failed: {result['error']}")
                return {
                    "success": False,
                    "data": {},
                    "error": result['error'],
                    "error_code": "TRADE_EXECUTION_FAILED",
                    "retry_after": 5
                }

        except Exception as e:
            logger.error(f"Error in buy_token: {e}")
            return {
                "success": False,
                "data": {},
                "error": str(e),
                "error_code": "UNEXPECTED_ERROR",
                "retry_after": 10
            }
    
    async def sell_token(self, token_address: str, token_amount: float = None, sell_percentage: str = None, slippage_percent: float = 10.0) -> Dict[str, Any]:
        """BULLETPROOF SELL: Execute sell order with multiple strategies and comprehensive error handling"""
        max_attempts = self.transaction_settings.get('max_sell_retries', 2)  # Configurable retry attempts
        original_balance = None  # Track original balance to detect successful sells

        for attempt in range(max_attempts):
            try:
                logger.info(f"🔴 BULLETPROOF SELL ATTEMPT {attempt + 1}/{max_attempts}: {sell_percentage or f'{token_amount} tokens'} of {token_address}")

                if not self.default_wallet:
                    logger.error("No wallet available for sell order")
                    return {
                        "success": False,
                        "data": {},
                        "error": "No wallet available for sell order",
                        "error_code": "NO_WALLET",
                        "retry_after": 0
                    }

                # FIXED: Reduced progressive slippage increases for retries
                base_slippage = slippage_percent
                current_slippage = base_slippage + (attempt * 2)  # Reduced from +5% to +2% per retry
                current_slippage = min(current_slippage, 15)  # Reduced cap from 50% to 15%

                if attempt > 0:
                    logger.info(f"🔄 RETRY {attempt + 1}: Increasing slippage to {current_slippage}% for better success rate")

                # CALCULATE EXTERNAL FEES FROM CONFIG (for sell operations)
                base_gas = self.transaction_settings.get('gas_price_sol', 0.0001)

                # Apply dynamic priority fee and retry escalation
                dynamic_base_gas = await self._calculate_dynamic_priority_fee(base_gas)
                gas_price = dynamic_base_gas * (1 + attempt * 0.5)  # +50% per retry on dynamic fee
                handling_fee_percent = self.transaction_settings.get('handling_fee_percent', 0.0)  # Fixed: No handling fee
                platform_fee_percent = self.transaction_settings.get('platform_fee_percent', 0.5)  # PumpPortal fee
                pumpfun_fee_percent = self.transaction_settings.get('pumpfun_fee_percent', 1.0)  # Pump.fun bonding curve fee

                # Note: For sells, percentage fees will be deducted from SOL received
                # Gas fee still needs to be available in wallet
                logger.info(f"💰 BULLETPROOF FEES: Gas: {gas_price:.4f} SOL, Handling: {handling_fee_percent}%, Platform: {platform_fee_percent}%")

                # Convert slippage from percentage to integer
                slippage_int = int(current_slippage)

                # BULLETPROOF: Get wallet public key for token balance checking
                private_key_bytes = base58.b58decode(self.default_wallet['private_key'])
                if len(private_key_bytes) == 64:
                    keypair = Keypair.from_bytes(private_key_bytes)
                elif len(private_key_bytes) == 32:
                    keypair = Keypair.from_seed(private_key_bytes)
                else:
                    keypair = Keypair.from_bytes(private_key_bytes)

                public_key = str(keypair.pubkey())

                # BULLETPROOF: Get current token balance with retries
                token_balance = await self._get_token_balance(public_key, token_address, retry_count=3)

                # Store original balance on first attempt
                if attempt == 0:
                    original_balance = token_balance
                    logger.info(f"🔍 ORIGINAL BALANCE: {original_balance:.6f} tokens")

                # CRITICAL FIX: Check if previous attempt already succeeded
                if token_balance == 0 and original_balance > 0 and attempt > 0:
                    logger.info(f"🎯 PREVIOUS SELL SUCCEEDED! Balance went from {original_balance:.6f} to 0 tokens")
                    logger.info(f"✅ SELL SUCCESS DETECTED: All tokens sold in previous attempt")
                    return {
                        "success": True,
                        "data": {
                            "transaction_signature": "previous_attempt_succeeded",
                            "token_amount": original_balance,
                            "gas_cost": self.transaction_settings.get('gas_price_sol', 0.0001),
                            "verified": True
                        },
                        "error": None,
                        "error_code": None,
                        "retry_after": 0
                    }

                if token_balance <= 0:
                    logger.warning(f"⚠️ BULLETPROOF: No token balance found for {token_address}! This likely means the sell was already executed successfully.")
                    logger.info(f"🎯 BULLETPROOF: Treating zero balance as successful sell completion")

                    # Try to get cached transaction hash from recent sells
                    cached_tx = self.recent_sell_transactions.get(token_address)
                    if cached_tx and (time.time() - cached_tx['timestamp']) < 3600:  # Within 1 hour
                        tx_hash = cached_tx['tx_hash']
                        logger.info(f"📝 Found cached sell transaction: {tx_hash}")
                    else:
                        tx_hash = "ALREADY_SOLD_ZERO_BALANCE"
                        logger.warning(f"⚠️ No recent cached transaction found for {token_address}")

                    # Return success since zero balance indicates the sell was already completed
                    return {
                        "success": True,
                        "data": {
                            "transaction_signature": tx_hash,
                            "token_amount": 0,
                            "gas_cost": 0,
                            "verified": True
                        },
                        "error": None,
                        "error_code": None,
                        "retry_after": 0
                    }

                logger.info(f"🔍 BULLETPROOF: Current token balance: {token_balance:.6f} tokens")

                # BULLETPROOF: ALWAYS SELL 100% OF ACTUAL WALLET BALANCE
                # CRITICAL FIX: Main bot ALWAYS passes token_amount, so we must override it
                sell_amount = "100%"
                denominated_in_sol = False

                # Log what we're overriding
                if sell_percentage:
                    logger.info(f"🎯 OVERRIDE: Ignoring sell_percentage '{sell_percentage}' - selling 100% of wallet")
                if token_amount is not None:
                    logger.info(f"🎯 OVERRIDE: Ignoring token_amount {token_amount:.6f} - selling 100% of wallet")
                    logger.info(f"🎯 OVERRIDE: Main bot wanted to sell {token_amount:.6f} but wallet has {token_balance:.6f}")

                logger.info(f"🎯 BULLETPROOF STRATEGY: Sell ALL tokens - 100% ({token_balance:.6f} tokens)")
                logger.info(f"🎯 REASON: ALWAYS sell complete wallet balance regardless of input parameters")

                # Store pre-sell balance for verification
                self._last_pre_sell_balance = token_balance
                logger.info(f"🔍 PRE-SELL BALANCE STORED: {self._last_pre_sell_balance:.6f} tokens")

                # BULLETPROOF: Execute the trade
                result = await self._execute_trade(
                    wallet=self.default_wallet,
                    action="sell",
                    mint=token_address,
                    amount=sell_amount,
                    denominated_in_sol=denominated_in_sol,
                    slippage=slippage_int,
                    external_fees=gas_price
                )

                # BULLETPROOF: Check if sell was successful
                if result["success"]:
                    logger.info(f"✅ BULLETPROOF SELL SUCCESS on attempt {attempt + 1}")

                    # CRITICAL FIX: Verify actual execution price vs expected
                    try:
                        await self._verify_execution_price(result, token_address, current_slippage)
                    except Exception as e:
                        logger.warning(f"⚠️ Execution price verification failed: {e}")

                    return await self._process_successful_sell(result, token_address, token_balance, gas_price)
                else:
                    logger.warning(f"⚠️ SELL ATTEMPT {attempt + 1} FAILED: {result.get('error', 'Unknown error')}")
                    if attempt < max_attempts - 1:
                        logger.info(f"🔄 Will retry with higher slippage in 3 seconds...")
                        await asyncio.sleep(3)
                        continue
                    else:
                        logger.error(f"❌ ALL {max_attempts} SELL ATTEMPTS FAILED")
                        return {
                            "success": False,
                            "data": {},
                            "error": f"All {max_attempts} sell attempts failed. Last error: {result.get('error', 'Unknown')}",
                            "error_code": "MAX_RETRIES_EXCEEDED",
                            "retry_after": 10
                        }

            except Exception as e:
                logger.error(f"❌ BULLETPROOF SELL ATTEMPT {attempt + 1} ERROR: {e}")
                if attempt < max_attempts - 1:
                    logger.info(f"🔄 Will retry in 5 seconds...")
                    await asyncio.sleep(5)
                    continue
                else:
                    logger.error(f"❌ ALL {max_attempts} SELL ATTEMPTS FAILED WITH EXCEPTIONS")
                    return {
                        "success": False,
                        "data": {},
                        "error": f"All sell attempts failed with exceptions. Last error: {str(e)}",
                        "error_code": "EXCEPTION_MAX_RETRIES",
                        "retry_after": 10
                    }

        # This should never be reached, but just in case
        return {
            "success": False,
            "data": {},
            "error": "Unexpected end of bulletproof sell function",
            "error_code": "UNEXPECTED_END",
            "retry_after": 10
        }

    async def _process_successful_sell(self, result: Dict[str, Any], token_address: str, pre_sell_balance: float, gas_price: float) -> Dict[str, Any]:
        """Process a successful sell result with verification"""
        try:
            tx_sig = result.get('transaction_signature', 'NO_SIGNATURE')
            verified = result.get('verified', False)
            logger.info(f"✅ Sell successful: {tx_sig} (Gas cost: {gas_price:.4f} SOL)")
            logger.info(f"🔍 CRITICAL DEBUG: Transaction verified on blockchain: {verified}")
            logger.info(f"🔗 Solscan link: https://solscan.io/tx/{tx_sig}")

            # BULLETPROOF: Double-check token balance after sell to verify it actually worked
            # CRITICAL FIX: Wait longer for blockchain state to update and use multiple verification attempts
            try:
                logger.info(f"🔍 Waiting 5 seconds for blockchain state to update...")
                await asyncio.sleep(5)  # Increased wait time for better reliability

                public_key = str(Keypair.from_bytes(base58.b58decode(self.default_wallet['private_key'])).pubkey())

                # BULLETPROOF: Try balance check multiple times with increasing delays
                post_sell_balance = None
                for balance_attempt in range(3):
                    post_sell_balance = await self._get_token_balance(public_key, token_address, retry_count=2)
                    if post_sell_balance is not None:
                        break
                    if balance_attempt < 2:
                        logger.warning(f"⚠️ Balance check attempt {balance_attempt + 1} failed, retrying in 2 seconds...")
                        await asyncio.sleep(2)

                # BULLETPROOF FIX: If balance check fails, the sell FAILED
                if post_sell_balance is None:
                    logger.error(f"❌ CRITICAL: Balance check failed - SELL FAILED")
                    logger.error(f"❌ Cannot verify if tokens were actually sold - treating as FAILURE")
                    logger.error(f"❌ This prevents phantom sell notifications when sells don't actually happen")
                    return {
                        "success": False,
                        "data": {},
                        "error": "Balance verification failed - cannot confirm sell execution",
                        "error_code": "BALANCE_VERIFICATION_FAILED",
                        "retry_after": 10
                    }

                logger.info(f"🔍 POST-SELL TOKEN BALANCE CHECK: {post_sell_balance:.6f} tokens remaining")

                # Calculate tokens sold based on pre-sell vs post-sell balance
                tokens_sold = pre_sell_balance - post_sell_balance

                # CRITICAL FIX: If we have way more tokens than expected, this might be multiple buys
                if pre_sell_balance > 1000:  # Large balance suggests multiple buys
                    logger.warning(f"🔍 LARGE WALLET BALANCE: {pre_sell_balance:.6f} tokens (suggests multiple buys)")
                    logger.warning(f"🔍 Any reduction in balance will be considered a successful sell")

                logger.info(f"🔍 SELL VERIFICATION: Pre-sell: {pre_sell_balance:.6f}, Post-sell: {post_sell_balance:.6f}, Sold: {tokens_sold:.6f}")

                # CRITICAL FIX: Check if ALL tokens were sold (post_sell_balance should be 0)
                if post_sell_balance > 0:
                    # Some tokens remain = partial sell or failed sell
                    if tokens_sold > 0:
                        logger.error(f"❌ PARTIAL SELL DETECTED: {tokens_sold:.6f} tokens sold, {post_sell_balance:.6f} remaining")
                        logger.error(f"❌ REQUIREMENT: 100% sell only - partial sells are FAILURES")
                        logger.error(f"❌ PumpPortal reported success but balance verification shows partial sell - REJECTING")
                        return {
                            "success": False,
                            "data": {},
                            "error": f"Partial sell rejected - {post_sell_balance:.6f} tokens remaining (need 100% sell)",
                            "error_code": "PARTIAL_SELL_REJECTED",
                            "retry_after": 0
                        }
                    else:
                        logger.error(f"❌ CRITICAL: SELL FAILED - No tokens sold! Balance: {post_sell_balance:.6f}")
                        logger.error(f"❌ This indicates the transaction failed or was fake!")
                        return {
                            "success": False,
                            "data": {},
                            "error": f"Sell failed - no tokens sold (balance: {post_sell_balance:.6f})",
                            "error_code": "SELL_VERIFICATION_FAILED",
                            "retry_after": 0
                        }
                else:
                    # post_sell_balance == 0 means ALL tokens sold = SUCCESS!
                    logger.info(f"✅ SELL SUCCESS: All {pre_sell_balance:.6f} tokens sold, balance now 0")

                    # Cache this successful transaction for future reference
                    self.recent_sell_transactions[token_address] = {
                        'tx_hash': tx_sig,
                        'timestamp': time.time()
                    }
                    logger.info(f"📝 Cached sell transaction for {token_address}: {tx_sig}")

                    # Clean up old cached transactions (older than 1 hour)
                    self._cleanup_transaction_cache()

                    return {
                            "success": True,
                            "data": {
                                "transaction_signature": tx_sig,
                                "token_amount": pre_sell_balance,  # All tokens sold
                                "gas_cost": gas_price,
                                "verified": verified
                            },
                            "error": "",
                            "error_code": "",
                            "retry_after": 0
                        }

            except Exception as balance_check_error:
                logger.error(f"❌ CRITICAL: Error checking post-sell balance: {balance_check_error}")
                logger.error(f"❌ BULLETPROOF: Cannot verify sell - treating as FAILURE")
                logger.error(f"❌ This prevents phantom sell notifications when verification fails")
                return {
                    "success": False,
                    "data": {},
                    "error": f"Balance verification failed: {balance_check_error}",
                    "error_code": "BALANCE_CHECK_ERROR",
                    "retry_after": 10
                }

        except Exception as e:
            logger.error(f"❌ Error processing successful sell: {e}")
            return {
                "success": False,
                "data": {},
                "error": f"Error processing sell result: {str(e)}",
                "error_code": "PROCESSING_ERROR",
                "retry_after": 5
            }


    
    async def get_balance(self) -> float:
        """Get SOL balance from wallet"""
        try:
            if not self.default_wallet:
                return 0.0

            # Create keypair to get public key
            private_key_bytes = base58.b58decode(self.default_wallet['private_key'])
            if len(private_key_bytes) == 64:
                keypair = Keypair.from_bytes(private_key_bytes)
            elif len(private_key_bytes) == 32:
                keypair = Keypair.from_seed(private_key_bytes)
            else:
                keypair = Keypair.from_bytes(private_key_bytes)

            public_key = str(keypair.pubkey())

            # Get balance via rate-limited RPC
            rpc_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [public_key]
            }

            response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=10)

            if response_data and 'result' in response_data:
                lamports = response_data['result']['value']
                sol_balance = lamports / 1_000_000_000  # Convert lamports to SOL
                return sol_balance

            return 0.0

        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return 0.0

    async def _get_token_balance(self, wallet_address: str, token_address: str, retry_count: int = 3) -> float:
        """BULLETPROOF: Get token balance with multiple fallbacks and retry logic"""
        for attempt in range(retry_count):
            try:
                logger.debug(f"🔍 Checking token balance for {token_address[:8]}... (attempt {attempt + 1}/{retry_count})")

                # Primary method: Helius DAS API
                payload = {
                    "jsonrpc": "2.0",
                    "id": f"helius-token-balance-{attempt}",
                    "method": "getTokenAccountsByOwner",
                    "params": [
                        wallet_address,
                        {"mint": token_address},
                        {"encoding": "jsonParsed"}
                    ]
                }

                response_data = self.helius_limiter.make_rpc_request(
                    self.helius_rpc_url, payload, timeout=20  # Increased timeout
                )

                if response_data and 'result' in response_data:
                    accounts = response_data['result']['value']
                    logger.debug(f"🔍 Found {len(accounts)} token accounts for {token_address[:8]}...")

                    if accounts:
                        # Get token amount from first account
                        account_data = accounts[0]['account']['data']['parsed']['info']
                        token_amount_info = account_data['tokenAmount']
                        ui_amount = token_amount_info['uiAmount']
                        raw_amount = token_amount_info['amount']
                        decimals = token_amount_info['decimals']

                        balance = float(ui_amount) if ui_amount else 0.0
                        logger.info(f"✅ Token balance found: {balance:.6f} tokens (raw: {raw_amount}, decimals: {decimals})")
                        return balance
                    else:
                        logger.debug(f"🔍 No token accounts found for {token_address[:8]}... - wallet has 0 balance")
                        return 0.0
                else:
                    logger.warning(f"⚠️ Invalid response from Helius API on attempt {attempt + 1}: {response_data}")
                    if attempt < retry_count - 1:
                        await asyncio.sleep(1 * (attempt + 1))  # Progressive delay
                        continue
                    return 0.0

            except Exception as e:
                logger.warning(f"⚠️ Error getting token balance on attempt {attempt + 1}: {e}")
                if attempt < retry_count - 1:
                    await asyncio.sleep(1 * (attempt + 1))  # Progressive delay
                    continue
                else:
                    logger.error(f"❌ All {retry_count} attempts failed for token balance - returning 0")
                    return 0.0

        return 0.0

    async def _get_latest_blockhash(self) -> Optional[str]:
        """Get latest blockhash with confirmed commitment for better reliability"""
        try:
            rpc_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getLatestBlockhash",
                "params": [{"commitment": "confirmed"}]  # Use confirmed for better reliability
            }

            response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=10)

            if response_data and 'result' in response_data and 'value' in response_data['result']:
                blockhash = response_data['result']['value']['blockhash']
                logger.debug(f"Fetched fresh blockhash: {blockhash[:16]}...")
                return blockhash
            else:
                logger.error("Failed to fetch latest blockhash")
                return None

        except Exception as e:
            logger.error(f"Error fetching latest blockhash: {e}")
            return None

    async def _calculate_dynamic_priority_fee(self, base_fee: float) -> float:
        """Calculate dynamic priority fee based on network congestion"""
        try:
            # Check if dynamic priority fees are enabled
            if not self.transaction_settings.get('use_dynamic_priority_fees', True):
                return base_fee

            max_multiplier = self.transaction_settings.get('max_priority_fee_multiplier', 3.0)
            # Get recent performance samples to assess network congestion
            rpc_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getRecentPerformanceSamples",
                "params": [5]  # Get last 5 samples
            }

            response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=5)

            if response_data and 'result' in response_data:
                samples = response_data['result']
                if samples:
                    # Calculate average transaction rate and slot time
                    total_transactions = sum(sample.get('numTransactions', 0) for sample in samples)
                    total_slots = sum(sample.get('numSlots', 1) for sample in samples)
                    avg_tx_per_slot = total_transactions / max(total_slots, 1)

                    # Determine congestion level
                    if avg_tx_per_slot > 3000:  # High congestion
                        multiplier = min(max_multiplier, 3.0)
                        congestion_level = "HIGH"
                    elif avg_tx_per_slot > 2000:  # Medium congestion
                        multiplier = min(max_multiplier, 2.0)
                        congestion_level = "MEDIUM"
                    elif avg_tx_per_slot > 1000:  # Low congestion
                        multiplier = min(max_multiplier, 1.5)
                        congestion_level = "LOW"
                    else:  # Normal
                        multiplier = 1.0
                        congestion_level = "NORMAL"

                    dynamic_fee = base_fee * multiplier

                    logger.info(f"[DYNAMIC FEE] Network congestion: {congestion_level} ({avg_tx_per_slot:.0f} tx/slot)")
                    logger.info(f"[DYNAMIC FEE] Priority fee: {base_fee:.6f} → {dynamic_fee:.6f} SOL ({multiplier}x)")

                    return dynamic_fee

            # Fallback to base fee if unable to determine congestion
            logger.debug("[DYNAMIC FEE] Unable to determine network congestion, using base fee")
            return base_fee

        except Exception as e:
            logger.warning(f"[DYNAMIC FEE] Error calculating dynamic priority fee: {e}")
            return base_fee

    async def _verify_execution_price(self, result: Dict, token_address: str, expected_slippage: float):
        """
        CRITICAL FIX: Verify actual execution price vs expected to detect excessive slippage
        """
        try:
            # Get transaction signature for analysis
            signature = result.get('signature')
            if not signature:
                logger.warning("[EXECUTION VERIFY] No signature found for price verification")
                return

            # Wait for transaction to be confirmed
            await asyncio.sleep(3)

            # Get actual SOL received from the transaction
            sol_received = result.get('sol_received', 0)
            tokens_sold = result.get('tokens_sold', 0)

            if sol_received > 0 and tokens_sold > 0:
                # Calculate actual execution price
                actual_price_per_token = sol_received / tokens_sold

                # Log execution details for analysis
                logger.info(f"[EXECUTION VERIFY] Actual execution price: {actual_price_per_token:.10f} SOL/token")
                logger.info(f"[EXECUTION VERIFY] SOL received: {sol_received:.6f}")
                logger.info(f"[EXECUTION VERIFY] Tokens sold: {tokens_sold:.0f}")
                logger.info(f"[EXECUTION VERIFY] Expected slippage tolerance: {expected_slippage:.2f}%")

                # Simple execution quality check based on slippage tolerance
                if expected_slippage > 15:
                    logger.warning(f"[EXECUTION VERIFY] ⚠️ HIGH SLIPPAGE TOLERANCE: {expected_slippage:.2f}% - consider reducing")
                elif expected_slippage > 10:
                    logger.info(f"[EXECUTION VERIFY] Moderate slippage tolerance: {expected_slippage:.2f}%")
                else:
                    logger.info(f"[EXECUTION VERIFY] ✅ Reasonable slippage tolerance: {expected_slippage:.2f}%")
            else:
                logger.warning(f"[EXECUTION VERIFY] Insufficient data for price verification: SOL={sol_received}, tokens={tokens_sold}")

        except Exception as e:
            logger.error(f"[EXECUTION VERIFY] Error verifying execution price: {e}")

    async def _verify_transaction_success(self, signature: str, max_wait_seconds: int = 30) -> bool:
        """CRITICAL: Verify transaction actually succeeded on blockchain"""
        try:
            start_time = time.time()

            while time.time() - start_time < max_wait_seconds:
                # Check transaction status
                rpc_payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getSignatureStatuses",
                    "params": [[signature], {"searchTransactionHistory": True}]
                }

                response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=10)

                if response_data and 'result' in response_data and 'value' in response_data['result']:
                    status_list = response_data['result']['value']
                    if status_list and status_list[0]:
                        status = status_list[0]

                        # Check if confirmed
                        if status.get('confirmationStatus') in ['confirmed', 'finalized']:
                            # CRITICAL: Check if successful (no error)
                            if status.get('err') is None:
                                logger.info(f"✅ Transaction VERIFIED successful: {signature}")
                                return True
                            else:
                                logger.error(f"❌ Transaction FAILED on blockchain: {signature} - {status['err']}")
                                return False

                await asyncio.sleep(2)

            logger.warning(f"⏰ Transaction verification TIMEOUT: {signature}")
            return False

        except Exception as e:
            logger.error(f"Verification error for {signature}: {e}")
            return False

    async def _confirm_transaction(self, signature: str, max_wait_seconds: int = 60) -> bool:
        """Legacy confirm method - use _verify_transaction_success instead"""
        return await self._verify_transaction_success(signature, max_wait_seconds)

    async def _execute_trade(self, wallet: Dict[str, Any], action: str, mint: str, amount,
                           denominated_in_sol: bool, slippage: int, external_fees: float = 0.0) -> Dict[str, Any]:
        """Execute trade via PumpPortal API"""
        try:
            # Create keypair from private key
            private_key_bytes = base58.b58decode(wallet['private_key'])
            
            if len(private_key_bytes) == 64:
                keypair = Keypair.from_bytes(private_key_bytes)
            elif len(private_key_bytes) == 32:
                keypair = Keypair.from_seed(private_key_bytes)
            else:
                keypair = Keypair.from_bytes(private_key_bytes)
            
            public_key = str(keypair.pubkey())

            # Get priority fee from config and ensure minimum for Helius staked connections
            config_priority_fee = self.transaction_settings.get('gas_price_sol', 0.0001)

            # Dynamic priority fee based on network congestion
            dynamic_priority_fee = await self._calculate_dynamic_priority_fee(config_priority_fee)

            # Helius requires minimum 10,000 lamports (0.00001 SOL) for staked connections
            min_priority_fee_sol = 0.00001  # 10,000 lamports
            priority_fee = max(dynamic_priority_fee, min_priority_fee_sol)

            logger.debug(f"Priority fee: {priority_fee:.8f} SOL ({priority_fee * 1_000_000_000:.0f} lamports)")

            # CRITICAL FIX: Keep percentage strings as strings for PumpPortal API
            # PumpPortal API requires: "100%" (string) for sells, NOT 100.0 (number)
            api_amount = amount
            if isinstance(amount, str) and amount.endswith('%'):
                # KEEP percentage as string - PumpPortal API expects "100%" not 100.0
                api_amount = amount  # Keep "100%" as "100%"
                logger.info(f"🔧 CRITICAL FIX: Keeping percentage as string: '{amount}' (PumpPortal requires string format)")
            elif isinstance(amount, str):
                # For non-percentage strings, try to convert to float
                try:
                    api_amount = float(amount)
                    logger.info(f"🔧 CRITICAL FIX: Converted non-percentage string '{amount}' to numeric {api_amount}")
                except ValueError:
                    logger.error(f"❌ CRITICAL: Invalid amount format: {amount}")
                    return {"success": False, "error": f"Invalid amount format: {amount}"}

            # Prepare PumpPortal API request (FIXED: Use JSON format per official documentation)
            payload = {
                "publicKey": public_key,
                "action": action,
                "mint": mint,
                "amount": api_amount,  # Use converted numeric amount
                "denominatedInSol": "true" if denominated_in_sol else "false",
                "slippage": slippage,
                "priorityFee": priority_fee,  # Use config-based priority fee
                "pool": "auto"
            }

            logger.info(f"🔍 DETAILED PumpPortal payload:")
            logger.info(f"   action: {action}")
            logger.info(f"   mint: {mint}")
            logger.info(f"   amount: {api_amount} (original: {amount})")
            logger.info(f"   denominatedInSol: {payload['denominatedInSol']}")
            logger.info(f"   slippage: {slippage}")
            logger.info(f"   priorityFee: {priority_fee} SOL")
            logger.info(f"   publicKey: {public_key}")

            # Get the correct API URL based on network mode
            api_url = self.get_current_api_url()
            logger.info(f"🌐 Using PumpPortal API: {api_url} (Network: {self.current_network_mode})")

            # Get transaction from PumpPortal (FIXED: Use JSON format per official documentation)
            response = requests.post(
                api_url,
                json=payload,  # Use json= instead of data=
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            logger.info(f"📡 PumpPortal API Response: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"❌ PumpPortal API Error Details:")
                logger.error(f"   Status Code: {response.status_code}")
                logger.error(f"   Response Text: {response.text}")
                logger.error(f"   Request URL: {api_url}")
                logger.error(f"   Request Payload: {payload}")

                return {
                    "success": False,
                    "error": f"PumpPortal API error: {response.status_code} - {response.text}"
                }
            
            # Get transaction bytes
            tx_bytes = response.content

            # Sign transaction - FIXED: Use official documentation method
            try:
                # Parse the transaction from bytes (per official docs)
                raw_transaction = VersionedTransaction.from_bytes(tx_bytes)

                # Sign the transaction using the official method
                signed_txn = VersionedTransaction(raw_transaction.message, [keypair])
                signed_tx_bytes = bytes(signed_txn)

            except Exception as signing_error:
                logger.error(f"Transaction signing failed: {signing_error}")
                return {"success": False, "error": f"Transaction signing failed: {signing_error}"}
            
            # Send transaction via RPC
            signed_tx_b64 = base64.b64encode(signed_tx_bytes).decode('utf-8')
            
            rpc_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    signed_tx_b64,
                    {
                        "encoding": "base64",
                        "preflightCommitment": "confirmed",  # Match blockhash commitment
                        "skipPreflight": True,               # FIXED: Skip preflight to avoid blockhash errors
                        "maxRetries": 0                      # FIXED: Use custom retry logic instead
                    }
                ]
            }
            
            # Send transaction with custom retry logic
            max_retries = 5
            base_delay = 2.0  # Start with 2 second delay

            for attempt in range(max_retries):
                try:
                    rpc_result = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=30)

                    if rpc_result and 'result' in rpc_result:
                        transaction_id = rpc_result['result']
                        logger.info(f"Transaction sent successfully on attempt {attempt + 1}: {transaction_id}")

                        # Transaction sent successfully - NOW WAIT FOR REAL VERIFICATION
                        logger.info(f"✅ Transaction sent successfully: {transaction_id}")
                        logger.info(f"🔍 CRITICAL FIX: Waiting for blockchain verification before confirming success...")

                        # PERMANENT FIX: Wait for actual blockchain verification
                        verification_success = await self._verify_transaction_success(transaction_id, max_wait_seconds=30)

                        if verification_success:
                            logger.info(f"✅ VERIFIED: Transaction confirmed on blockchain: {transaction_id}")
                            return {
                                "success": True,
                                "transaction_signature": transaction_id,
                                "solscan_url": f"https://solscan.io/tx/{transaction_id}",
                                "verified": True  # Actually verified on blockchain
                            }
                        else:
                            logger.error(f"❌ CRITICAL: Transaction FAILED verification on blockchain: {transaction_id}")
                            return {
                                "success": False,
                                "transaction_signature": transaction_id,
                                "solscan_url": f"https://solscan.io/tx/{transaction_id}",
                                "verified": False,
                                "error": "Transaction failed blockchain verification"
                            }
                    elif rpc_result and 'error' in rpc_result:
                        error_msg = rpc_result.get('error', {}).get('message', 'Unknown RPC error')
                        logger.warning(f"RPC error on attempt {attempt + 1}: {error_msg}")

                        # Don't retry on certain errors
                        if any(err in error_msg.lower() for err in ['invalid', 'signature', 'blockhash not found']):
                            return {"success": False, "error": f"RPC error: {error_msg}"}

                        # Retry on rate limiting or network errors
                        if attempt < max_retries - 1:
                            delay = base_delay * (2 ** attempt)  # Exponential backoff
                            logger.info(f"Retrying in {delay} seconds...")
                            await asyncio.sleep(delay)
                            continue
                        else:
                            return {"success": False, "error": f"RPC error after {max_retries} attempts: {error_msg}"}
                    else:
                        logger.warning(f"No result from RPC on attempt {attempt + 1}")
                        if attempt < max_retries - 1:
                            delay = base_delay * (2 ** attempt)
                            logger.info(f"Retrying in {delay} seconds...")
                            await asyncio.sleep(delay)
                            continue
                        else:
                            return {
                                "success": False,
                                "error": f"RPC request failed after {max_retries} attempts - rate limited or network error"
                            }

                except Exception as retry_error:
                    logger.error(f"Error on retry attempt {attempt + 1}: {retry_error}")
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        logger.info(f"Retrying in {delay} seconds...")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        return {"success": False, "error": f"Failed after {max_retries} attempts: {retry_error}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
