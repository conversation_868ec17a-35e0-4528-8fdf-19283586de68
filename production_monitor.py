#!/usr/bin/env python3
"""
Production Monitor - Zero-Risk Monitoring for Trading Bot
Operator's Rule: Monitor everything, break nothing
"""

import asyncio
import logging
import time
import os
import psutil
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class SystemHealth:
    """System health metrics"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    disk_free_gb: float
    uptime_hours: float
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class TradingHealth:
    """Trading system health metrics"""
    timestamp: str
    telegram_connected: bool
    signal_queue_size: int
    active_positions: int
    available_sol: float
    total_trades: int
    last_signal_time: Optional[str]
    last_trade_time: Optional[str]
    error_count_1h: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ProductionMonitor:
    """Production monitoring without touching core trading logic"""
    
    def __init__(self, bot_controller=None):
        self.bot_controller = bot_controller
        self.start_time = time.time()
        self.error_count = 0
        self.last_error_reset = time.time()
        self.health_log_file = "production_health.jsonl"
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_free_gb': 1.0,
            'error_rate_1h': 10,
            'signal_queue_size': 100
        }
        
    def get_system_health(self) -> SystemHealth:
        """Get system resource health"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)  # Shorter interval
            memory = psutil.virtual_memory()

            # Fix disk usage for Windows
            try:
                if os.name == 'nt':  # Windows
                    disk = psutil.disk_usage('C:')
                else:
                    disk = psutil.disk_usage('/')
                disk_free_gb = disk.free / (1024 * 1024 * 1024)
            except Exception:
                disk_free_gb = 100.0  # Default safe value

            uptime_hours = (time.time() - self.start_time) / 3600

            return SystemHealth(
                timestamp=datetime.now(timezone.utc).isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available_mb=memory.available / (1024 * 1024),
                disk_free_gb=disk_free_gb,
                uptime_hours=uptime_hours
            )
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return SystemHealth(
                timestamp=datetime.now(timezone.utc).isoformat(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_available_mb=0.0,
                disk_free_gb=100.0,  # Safe default
                uptime_hours=0.0
            )
    
    def get_trading_health(self) -> TradingHealth:
        """Get trading system health"""
        try:
            if not self.bot_controller:
                return TradingHealth(
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    telegram_connected=False,
                    signal_queue_size=0,
                    active_positions=0,
                    available_sol=0.0,
                    total_trades=0,
                    last_signal_time=None,
                    last_trade_time=None,
                    error_count_1h=self.get_error_count_1h()
                )
            
            # Safely get metrics without breaking anything
            # Fix: Accept both "CONNECTED" and "LISTENING" as valid connected states
            telegram_connected = False
            if (hasattr(self.bot_controller, 'signal_handler') and
                self.bot_controller.signal_handler and
                hasattr(self.bot_controller.signal_handler, 'telegram_status')):

                status = self.bot_controller.signal_handler.telegram_status
                # Telegram status values:
                # "DISCONNECTED" - Not connected
                # "CONNECTED" - Connected but not listening yet
                # "LISTENING" - Connected AND actively listening for messages (best state)
                # "RECONNECTING" - Temporarily reconnecting
                # "ERROR" - Connection error
                # "UNAUTHORIZED" - Not authorized
                # Consider both "CONNECTED" and "LISTENING" as valid connected states
                telegram_connected = status in ["CONNECTED", "LISTENING"]
            
            signal_queue_size = 0
            if (hasattr(self.bot_controller, 'signal_handler') and 
                self.bot_controller.signal_handler and
                hasattr(self.bot_controller.signal_handler, 'signal_queue')):
                try:
                    signal_queue_size = self.bot_controller.signal_handler.signal_queue.qsize()
                except:
                    signal_queue_size = 0
            
            active_positions = 0
            available_sol = 0.0
            total_trades = 0
            if (hasattr(self.bot_controller, 'state_manager') and 
                self.bot_controller.state_manager):
                try:
                    active_positions = len([p for p in self.bot_controller.state_manager.positions.values() 
                                          if p.get('status') == 'OPEN'])
                    available_sol = self.bot_controller.state_manager.available_sol
                    total_trades = self.bot_controller.state_manager.total_trades
                except:
                    pass
            
            return TradingHealth(
                timestamp=datetime.now(timezone.utc).isoformat(),
                telegram_connected=telegram_connected,
                signal_queue_size=signal_queue_size,
                active_positions=active_positions,
                available_sol=available_sol,
                total_trades=total_trades,
                last_signal_time=None,  # TODO: Track this
                last_trade_time=None,   # TODO: Track this
                error_count_1h=self.get_error_count_1h()
            )
            
        except Exception as e:
            logger.error(f"Error getting trading health: {e}")
            return TradingHealth(
                timestamp=datetime.now(timezone.utc).isoformat(),
                telegram_connected=False,
                signal_queue_size=0,
                active_positions=0,
                available_sol=0.0,
                total_trades=0,
                last_signal_time=None,
                last_trade_time=None,
                error_count_1h=self.get_error_count_1h()
            )
    
    def get_error_count_1h(self) -> int:
        """Get error count in last hour"""
        current_time = time.time()
        if current_time - self.last_error_reset > 3600:  # 1 hour
            self.error_count = 0
            self.last_error_reset = current_time
        return self.error_count
    
    def record_error(self):
        """Record an error occurrence"""
        self.error_count += 1
    
    def check_alerts(self, system_health: SystemHealth, trading_health: TradingHealth) -> list:
        """Check for alert conditions"""
        alerts = []
        
        # System alerts
        if system_health.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append(f"HIGH CPU: {system_health.cpu_percent:.1f}%")
        
        if system_health.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append(f"HIGH MEMORY: {system_health.memory_percent:.1f}%")
        
        if system_health.disk_free_gb < self.alert_thresholds['disk_free_gb']:
            alerts.append(f"LOW DISK: {system_health.disk_free_gb:.1f}GB")
        
        # Trading alerts
        # COSMETIC FIX: Telegram disconnection doesn't affect trading functionality
        # if not trading_health.telegram_connected:
        #     alerts.append("TELEGRAM DISCONNECTED")
        
        if trading_health.signal_queue_size > self.alert_thresholds['signal_queue_size']:
            alerts.append(f"SIGNAL QUEUE BACKLOG: {trading_health.signal_queue_size}")
        
        if trading_health.error_count_1h > self.alert_thresholds['error_rate_1h']:
            alerts.append(f"HIGH ERROR RATE: {trading_health.error_count_1h}/hour")
        
        return alerts
    
    def log_health(self, system_health: SystemHealth, trading_health: TradingHealth, alerts: list):
        """Log health metrics to file"""
        try:
            health_data = {
                'system': system_health.to_dict(),
                'trading': trading_health.to_dict(),
                'alerts': alerts
            }
            
            with open(self.health_log_file, 'a') as f:
                f.write(json.dumps(health_data) + '\n')
                
        except Exception as e:
            logger.error(f"Error logging health data: {e}")
    
    async def monitor_loop(self):
        """Main monitoring loop - runs every 60 seconds"""
        logger.info("Production monitor started")
        
        while True:
            try:
                # Get health metrics
                system_health = self.get_system_health()
                trading_health = self.get_trading_health()
                
                # Check for alerts
                alerts = self.check_alerts(system_health, trading_health)
                
                # Log everything
                self.log_health(system_health, trading_health, alerts)
                
                # Log alerts to console
                if alerts:
                    logger.warning(f"PRODUCTION ALERTS: {', '.join(alerts)}")
                else:
                    logger.debug(f"System healthy - CPU: {system_health.cpu_percent:.1f}%, "
                               f"Memory: {system_health.memory_percent:.1f}%, "
                               f"Positions: {trading_health.active_positions}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                self.record_error()
                await asyncio.sleep(60)

# Global monitor instance
_production_monitor = None

def get_production_monitor(bot_controller=None) -> ProductionMonitor:
    """Get or create production monitor"""
    global _production_monitor
    if _production_monitor is None:
        _production_monitor = ProductionMonitor(bot_controller)
    return _production_monitor

def start_production_monitoring(bot_controller=None):
    """Start production monitoring task"""
    monitor = get_production_monitor(bot_controller)
    asyncio.create_task(monitor.monitor_loop())
    logger.info("Production monitoring started")
