#!/usr/bin/env python3
"""
Test the actual Solana Early Trending message that was missed
"""

import re
import json
import asyncio
import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from signal_handler import SignalHandler
    from config_manager import ConfigManager
    print("✅ Successfully imported signal handler modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)

# The actual message that was missed
ACTUAL_MESSAGE = """🔥 justice for jester (https://t.me/soul_sniper_bot?start=15_ECF94hntRyZKftWnjH5GyQhfTeYV5x3JwDRMZHpnpump) New Trending (https://t.me/solearlytrending)
🕒 Age: 23m | Security: ⚠️
🔗 X (https://x.com/DailyMail/status/1939669816870117474)•CHART (https://www.geckoterminal.com/solana/tokens/ECF94hntRyZKftWnjH5GyQhfTeYV5x3JwDRMZHpnpump)

💰 MC: $233,008 • 🔝 $577.1K
💧 Liq: $51K
📈 Vol: 1h: $753.9K
👥 Hodls: 2011

🔫 Snipers: 4 • 26.8% ⚠️
🎯 First 20 (https://t.me/soul_scanner_bot?start=first20_ECF94hntRyZKftWnjH5GyQhfTeYV5x3JwDRMZHpnpump): 0.16% | 7 🐟 • 0.08%
🐟 (https://solscan.io/account/6d22FozaKK239PoBYVffkYKA1QPQZE8fC7AQkpmHQfjp)🍤 (https://solscan.io/account/6vdNH2HedEvBtFJ2ULMpG96QBKJ2gWidAoEE21pHdPca)🍤 (https://solscan.io/account/J4XZVj7HGzzcaRCTvhwfN2cap2SYxoq8RccYn86sQq5z)🍤 (https://solscan.io/account/CtgpsQdUTnU16vLD1qxNXdZpk3k4armyMj8Mns8ZwrYP)🐟 (https://solscan.io/account/AK47fTuasQZE4bHaFLmgH5iMFQLZ9VhSJmK3prqQh4HM)🐟 (https://solscan.io/account/BiGJ8Nz9WZucpHJu37XJpWBCE9atM52ga66U53jQ53zK)🍤 (https://solscan.io/account/CYugyi4aMRrZWn9zviQXYLF9tRJF9QNZyQvGe67MLQfQ)🐟 (https://solscan.io/account/5sH4qTUyEMnWTJYp8BdowJzqM65PcXdW6zRnpKjMNXGp)🍤 (https://solscan.io/account/4EHuNzQmPWe2ex9DZNBWbkynnESvr8mP5VsknvHL1EH5)🐟 (https://solscan.io/account/HN7w9ndQthDWsDHh3JPrzEyKfezPFckgBQ96uPPhgzEn)
🍤 (https://solscan.io/account/4BfD2kJbJj9vU4m12AS1Q2MfezfYVoGZm9KxKVwtYHdd)🐟 (https://solscan.io/account/EHnvGvBAz9HX9Jwwkwmita3bThwyXqLMvb99nW6Awntz)🍤 (https://solscan.io/account/2EoXa97YyNQCRe8yNxK8FnTXrmstNwhCYfgAqx1ofRtc)🐟 (https://solscan.io/account/F3m2C4biThKXpPt6zbXW3VbXwucK8Rhv1Dtyrh1ruzUz)

🛠️ Dev: (https://solscan.io/account/A6p2oyNN5YSrrRkJCRbCD6pxnm3JrGA6vtPbdvuZ7pby) 0 SOL | 0% $JESTER
┣ Bundled: 26.8% ⚠️
┗ Airdrops: 0% 🤍"""

EXPECTED_TOKEN = "ECF94hntRyZKftWnjH5GyQhfTeYV5x3JwDRMZHpnpump"

def test_regex_patterns():
    """Test regex patterns on the actual message"""
    print("🔍 TESTING REGEX PATTERNS ON ACTUAL MESSAGE")
    print("=" * 60)
    print(f"Expected token: {EXPECTED_TOKEN}")
    print(f"Message length: {len(ACTUAL_MESSAGE)} characters")
    print()
    
    # Test the soul_sniper pattern specifically
    soul_sniper_pattern = r'https://t\.me/soul_sniper_bot\?start=15_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
    match = re.search(soul_sniper_pattern, ACTUAL_MESSAGE)
    if match:
        token = match.group(1)
        print(f"✅ Soul Sniper pattern: {token}")
        print(f"   Correct: {'✅' if token == EXPECTED_TOKEN else '❌'}")
    else:
        print("❌ Soul Sniper pattern: No match found")
    
    # Test other patterns
    patterns = [
        (r'([1-9A-HJ-NP-Za-km-z]{32,44}pump)', "Pump.fun pattern"),
        (r'([1-9A-HJ-NP-Za-km-z]{32,44})', "General Solana address"),
    ]
    
    for pattern, description in patterns:
        matches = re.findall(pattern, ACTUAL_MESSAGE)
        if matches:
            print(f"✅ {description}: Found {len(matches)} match(es)")
            for i, match in enumerate(matches[:3]):
                is_expected = match == EXPECTED_TOKEN
                status = "✅ CORRECT" if is_expected else "❌ WRONG"
                print(f"   Match {i+1}: {match} {status}")
        else:
            print(f"❌ {description}: No matches found")

def test_signal_handler_extraction():
    """Test the signal handler extraction methods"""
    print("\n🔍 TESTING SIGNAL HANDLER EXTRACTION")
    print("=" * 60)
    
    config_manager = ConfigManager()
    signal_handler = SignalHandler(config_manager)
    
    # Test with Early Trending channel ID
    early_trending_id = -1002093384030
    channel_info = {'channel_id': early_trending_id}
    
    print(f"Testing with channel_id: {early_trending_id}")
    print(f"signal_handler.solearlytrending_channel_id: {signal_handler.solearlytrending_channel_id}")
    print()
    
    # Test extract_token_address method
    print("Testing extract_token_address method:")
    try:
        token = signal_handler.extract_token_address(ACTUAL_MESSAGE, channel_info)
        if token:
            print(f"  ✅ Found token: {token}")
            print(f"  Correct: {'✅' if token == EXPECTED_TOKEN else '❌'}")
        else:
            print(f"  ❌ No token found")
    except Exception as e:
        print(f"  ❌ ERROR: {e}")
    
    # Test _extract_token_addresses method
    print("\nTesting _extract_token_addresses method:")
    try:
        tokens = signal_handler._extract_token_addresses(ACTUAL_MESSAGE, early_trending_id)
        if tokens:
            print(f"  ✅ Found tokens: {tokens}")
            print(f"  First token correct: {'✅' if tokens[0] == EXPECTED_TOKEN else '❌'}")
        else:
            print(f"  ❌ No tokens found")
    except Exception as e:
        print(f"  ❌ ERROR: {e}")

async def test_full_signal_extraction():
    """Test the complete signal extraction pipeline"""
    print("\n🔍 TESTING FULL SIGNAL EXTRACTION PIPELINE")
    print("=" * 60)
    
    config_manager = ConfigManager()
    signal_handler = SignalHandler(config_manager)
    
    # Create message object as the signal handler expects
    message_obj = {
        'text': ACTUAL_MESSAGE,
        'chat_id': -1002093384030,  # Solana Early Trending channel ID
        'date': None,
        'is_gmgn_channel': False,
        'channel_identifier': None
    }
    
    try:
        # Test the full _extract_signal method
        signal = await signal_handler._extract_signal(message_obj)
        
        if signal:
            extracted_token = signal.get('token_address') or signal.get('token')
            source_type = signal.get('source_type')
            
            print(f"✅ Full pipeline SUCCESS")
            print(f"   Token: {extracted_token}")
            print(f"   Source: {source_type}")
            print(f"   Correct token: {'✅' if extracted_token == EXPECTED_TOKEN else '❌'}")
            print(f"   Signal keys: {list(signal.keys())}")
        else:
            print(f"❌ Full pipeline FAILED - No signal extracted")
            
    except Exception as e:
        print(f"❌ Full pipeline ERROR: {e}")
        import traceback
        traceback.print_exc()

def analyze_message_structure():
    """Analyze the structure of the message"""
    print("\n🔍 ANALYZING MESSAGE STRUCTURE")
    print("=" * 60)
    
    lines = ACTUAL_MESSAGE.split('\n')
    print(f"Total lines: {len(lines)}")
    print()
    
    for i, line in enumerate(lines[:5]):  # Show first 5 lines
        print(f"Line {i+1}: {line}")
        
        # Check if this line contains the soul_sniper link
        if 'soul_sniper_bot' in line:
            print(f"   ⭐ Contains soul_sniper_bot link")
            
            # Extract the token from this line specifically
            soul_sniper_pattern = r'https://t\.me/soul_sniper_bot\?start=15_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
            match = re.search(soul_sniper_pattern, line)
            if match:
                token = match.group(1)
                print(f"   ✅ Extracted token: {token}")
            else:
                print(f"   ❌ Could not extract token from this line")

async def main():
    """Run all tests"""
    print("🧪 TESTING ACTUAL SOLANA EARLY TRENDING MESSAGE")
    print("=" * 60)
    
    # Analyze message structure
    analyze_message_structure()
    
    # Test regex patterns
    test_regex_patterns()
    
    # Test signal handler extraction
    test_signal_handler_extraction()
    
    # Test full signal extraction pipeline
    await test_full_signal_extraction()
    
    print(f"\n🏁 Testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
