import csv
from typing import Dict, Any, Optional
from datetime import datetime
import os
import logging
import time
import asyncio

logger = logging.getLogger(__name__)

# CSV file paths
ERROR_LOG = "error_log.csv"
SKIPPED_TOKENS = "skipped_tokens.csv"
SIGNAL_LOG = "signal_log.csv"
PERFORMANCE_SUMMARY = "performance_summary.csv"
HIGH_PERFORMING_TOKENS = "high_performing_tokens.csv"
TRADE_LOG = "trade_log.csv"
HISTORICAL_SIGNALS = "historical_signals.csv"
SENTIMENT_CORRELATIONS = "sentiment_correlations.csv"
SENTIMENT_DATA = "sentiment_data.csv"

# --- CSV Logging Utilities ---
LOG_LOCK = asyncio.Lock() # Use asyncio lock if used in async context



def log_to_csv(filename: str, data: Dict[str, Any]):
    """Append data dictionary to a CSV file with locking and atomic writing."""
    # Use async with LOG_LOCK: # Uncomment if log_to_csv is called from async functions
    try:
        # Ensure directory exists if filename includes path
        dir_name = os.path.dirname(filename)
        if dir_name:
            os.makedirs(dir_name, exist_ok=True)

        # Check if file exists to determine if header is needed
        file_exists = os.path.isfile(filename)

        # Create a temporary file for atomic writing
        temp_filename = f"{filename}.tmp"

        # If file doesn't exist, create it with header
        if not file_exists:
            with open(temp_filename, 'w', newline='', encoding='utf-8', errors='replace') as csvfile:
                fieldnames = list(data.keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                # Write the data safely
                try:
                    writer.writerow(data)
                except UnicodeEncodeError:
                    # Handle Unicode encoding issues by replacing problematic characters
                    safe_data = {}
                    for key, value in data.items():
                        if isinstance(value, str):
                            safe_data[key] = value.encode('utf-8', errors='replace').decode('utf-8')
                        else:
                            safe_data[key] = value
                    writer.writerow(safe_data)

                # Ensure data is written to disk
                csvfile.flush()
                os.fsync(csvfile.fileno())

            # Atomic rename
            os.replace(temp_filename, filename)
        else:
            # File exists, append to it
            # First read existing content
            existing_content = ""
            try:
                with open(filename, 'r', encoding='utf-8', errors='replace') as f:
                    existing_content = f.read()
            except Exception as read_error:
                logger.warning(f"Error reading existing file '{filename}': {read_error}")
                # If we can't read the file, assume it's empty or corrupted
                existing_content = ""

            # Write to temporary file
            with open(temp_filename, 'w', newline='', encoding='utf-8', errors='replace') as csvfile:
                fieldnames = list(data.keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # If existing content is empty or doesn't contain a header, write the header
                if not existing_content:
                    writer.writeheader()
                else:
                    # Write existing content first
                    csvfile.write(existing_content)

                # Write the new data safely
                try:
                    writer.writerow(data)
                except UnicodeEncodeError:
                    # Handle Unicode encoding issues by replacing problematic characters
                    safe_data = {}
                    for key, value in data.items():
                        if isinstance(value, str):
                            safe_data[key] = value.encode('utf-8', errors='replace').decode('utf-8')
                        else:
                            safe_data[key] = value
                    writer.writerow(safe_data)

                # Ensure data is written to disk
                csvfile.flush()
                os.fsync(csvfile.fileno())

            # Atomic rename
            os.replace(temp_filename, filename)

    except Exception as e:
        logger.error(f"Error logging to CSV file '{filename}': {e}")
        # Try a simpler approach as fallback
        try:
            with open(filename, 'a', newline='', encoding='utf-8', errors='replace') as csvfile:
                fieldnames = list(data.keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                if not os.path.getsize(filename):
                    writer.writeheader()

                # Write the data with safe encoding
                safe_data = {}
                for key, value in data.items():
                    if isinstance(value, str):
                        safe_data[key] = value.encode('utf-8', errors='replace').decode('utf-8')
                    else:
                        safe_data[key] = value
                writer.writerow(safe_data)
        except Exception as fallback_error:
            logger.error(f"Fallback logging also failed for '{filename}': {fallback_error}")

def log_error(error_type: str, message: str, context: Optional[str] = None, filename: str = "error_log.csv"):
    """Log an error event."""
    log_data = {
        'timestamp': datetime.now().isoformat(),
        'error_type': error_type,
        'message': message,
        'context': context if context else ''
    }
    # Use logger for console/file handler, and also log to dedicated CSV
    logger.error(f"{error_type}: {message} [Context: {context}]" if context else f"{error_type}: {message}")
    log_to_csv(filename, log_data)

def log_skipped_token(token_address: str, reason: str):
    """Log skipped token to skipped_tokens.csv"""
    log_to_csv(SKIPPED_TOKENS, {
        "timestamp": datetime.now().isoformat(),
        "token_address": token_address,
        "reason": reason
    })

def log_signal(signal_type: str, token_address: str, confidence: float, price: float, volume: float):
    """Log signal to signal_log.csv"""
    log_to_csv(SIGNAL_LOG, {
        "timestamp": datetime.now().isoformat(),
        "signal_type": signal_type,
        "token_address": token_address,
        "confidence": confidence,
        "price": price,
        "volume": volume
    })

def log_trade(trade_type: str, token_address: str, amount: float, price: float, pnl: float = 0.0, details: str = ""):
    """Log trade to trade_log.csv (legacy function, use log_trade_confirmed for new code)"""
    log_to_csv(TRADE_LOG, {
        "timestamp": datetime.now().isoformat(),
        "trade_type": trade_type,
        "token_address": token_address,
        "amount": amount,
        "price": price,
        "pnl": pnl,
        "details": details
    })

def log_trade_confirmed(action: str, token: str, qty: float, entry_price: float, exit_price: float = None,
                        sol_balance: float = None, reason: str = "", token_name: str = "", confirmed: bool = True):
    """
    Log a confirmed trade with precise PNL calculation.

    Args:
        action: 'BUY' or 'SELL'
        token: Token address
        qty: Token quantity
        entry_price: Entry price (for both buy and sell)
        exit_price: Exit price (for sell only)
        sol_balance: Current SOL balance
        reason: Reason for the trade
        token_name: Token name or symbol
        confirmed: Whether the trade is confirmed (only log if True)
    """
    if not confirmed:
        logger.warning(f"Skipping unconfirmed trade log for {token}")
        return

    timestamp = datetime.now().isoformat()

    if action.upper() == 'BUY':
        log_to_csv(TRADE_LOG, {
            "timestamp": timestamp,
            "action": "BUY",
            "token": token,
            "token_name": token_name,
            "quantity": qty,
            "price": entry_price,
            "sol_spent": qty * entry_price,
            "sol_balance": sol_balance if sol_balance is not None else 0.0,
            "reason": reason
        })
    elif action.upper() == 'SELL':
        if exit_price is None:
            logger.error(f"Cannot log SELL without exit_price for {token}")
            return

        # Calculate PNL using precise Decimal math
        pnl_sol, pnl_percent = calculate_precise_pnl(entry_price, exit_price, qty)

        log_to_csv(TRADE_LOG, {
            "timestamp": timestamp,
            "action": "SELL",
            "token": token,
            "token_name": token_name,
            "quantity": qty,
            "entry_price": entry_price,
            "exit_price": exit_price,
            "sol_received": qty * exit_price,
            "pnl_sol": pnl_sol,
            "pnl_percent": pnl_percent,
            "sol_balance": sol_balance if sol_balance is not None else 0.0,
            "reason": reason
        })

def log_performance(token_address: str, entry_price: float, exit_price: float, pnl: float, duration: float):
    """Log performance to performance_summary.csv"""
    log_to_csv(PERFORMANCE_SUMMARY, {
        "timestamp": datetime.now().isoformat(),
        "token_address": token_address,
        "entry_price": entry_price,
        "exit_price": exit_price,
        "pnl": pnl,
        "duration_minutes": duration
    })

def log_high_performing_token(token_address: str, pnl: float, volume: float):
    """Log high performing token to high_performing_tokens.csv"""
    log_to_csv(HIGH_PERFORMING_TOKENS, {
        "timestamp": datetime.now().isoformat(),
        "token_address": token_address,
        "pnl": pnl,
        "volume": volume
    })

# Simulation logging removed - real trading only

def log_historical_signal(token_address: str, signal_type: str, price: float, volume: float):
    """Log historical signal to historical_signals.csv"""
    log_to_csv(HISTORICAL_SIGNALS, {
        "timestamp": datetime.now().isoformat(),
        "token_address": token_address,
        "signal_type": signal_type,
        "price": price,
        "volume": volume
    })

def log_sentiment_data(token_address: str, sentiment_score: float, volume: float):
    """Log sentiment data to sentiment_data.csv"""
    log_to_csv(SENTIMENT_DATA, {
        "timestamp": datetime.now().isoformat(),
        "token_address": token_address,
        "sentiment_score": sentiment_score,
        "volume": volume
    })

def log_sentiment_correlation(token_address: str, correlation_score: float):
    """Log sentiment correlation to sentiment_correlations.csv"""
    log_to_csv(SENTIMENT_CORRELATIONS, {
        "timestamp": datetime.now().isoformat(),
        "token_address": token_address,
        "correlation_score": correlation_score
    })

def log_signal_data(signal_data: Dict[str, Any], filename: str = "signal_log.csv"):
    """Log raw signal data."""
    log_data = {
        'timestamp': datetime.fromtimestamp(signal_data.get('timestamp', time.time())).isoformat(),
        'token_address': signal_data.get('token_address'),
        'source_channel': signal_data.get('source_channel'),
        'message_text': signal_data.get('message_text', '').replace('\n', ' '), # Replace newlines for CSV
        'is_gmgn': signal_data.get('metadata', {}).get('is_gmgn', False),
        'is_early': signal_data.get('metadata', {}).get('is_early', False)
    }
    log_to_csv(filename, log_data)

def format_sol_amount(amount: float) -> str:
    """Format SOL amount with appropriate precision"""
    if amount >= 1:
        return f"{amount:.2f} SOL"
    elif amount >= 0.01:
        return f"{amount:.4f} SOL"
    else:
        return f"{amount:.8f} SOL"

def format_usd_amount(amount: float) -> str:
    """Format USD amount with appropriate precision"""
    if amount >= 1000:
        return f"${amount/1000:.1f}K"
    elif amount >= 1:
        return f"${amount:.2f}"
    else:
        return f"${amount:.4f}"



def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change"""
    if old_value == 0:
        return 0.0
    return ((new_value - old_value) / old_value) * 100

def format_percentage(value: float) -> str:
    """Format percentage with sign"""
    return f"{'+' if value > 0 else ''}{value:.2f}%"

def calculate_pnl(entry_price: float, exit_price: float, amount: float) -> float:
    """Calculate profit/loss"""
    return (exit_price - entry_price) * amount

def calculate_precise_pnl(entry_price, exit_price, amount):
    """
    Calculate profit/loss using Decimal for maximum precision.

    Args:
        entry_price: Entry price
        exit_price: Exit price
        amount: Token amount

    Returns:
        Tuple of (pnl_sol, pnl_percent)
    """
    from decimal import Decimal, ROUND_DOWN

    # Convert inputs to Decimal for precision
    entry_price_dec = Decimal(str(entry_price))
    exit_price_dec = Decimal(str(exit_price))
    amount_dec = Decimal(str(amount))

    # Calculate PNL in SOL
    pnl_sol = (exit_price_dec - entry_price_dec) * amount_dec

    # Calculate percentage change
    if entry_price_dec > 0:
        pnl_percent = ((exit_price_dec / entry_price_dec) - Decimal('1')) * Decimal('100')
    else:
        pnl_percent = Decimal('0')

    # Round to reasonable precision
    pnl_sol = pnl_sol.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
    pnl_percent = pnl_percent.quantize(Decimal('0.01'), rounding=ROUND_DOWN)

    return float(pnl_sol), float(pnl_percent)

def format_timestamp(timestamp: str) -> str:
    """Format ISO timestamp to readable format"""
    try:
        dt = datetime.fromisoformat(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return timestamp

def safe_write(filepath: str, content: str):
    """
    Write content to a file using atomic operations to prevent corruption.

    Args:
        filepath: Path to the file to write
        content: Content to write to the file
    """
    import tempfile
    import shutil

    # Ensure directory exists
    dir_name = os.path.dirname(filepath)
    if dir_name:
        os.makedirs(dir_name, exist_ok=True)

    # Write to temporary file first
    with tempfile.NamedTemporaryFile('w', delete=False, encoding='utf-8', errors='replace') as tmp:
        tmp.write(content)
        tmp.flush()
        os.fsync(tmp.fileno())

    # Atomic move to destination
    shutil.move(tmp.name, filepath)