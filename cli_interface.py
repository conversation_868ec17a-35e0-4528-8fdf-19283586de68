import asyncio
import os
import logging
from bot_controller import BotController
from wallet_manager import WalletManager
import functools

logger = logging.getLogger(__name__)

def show_main_menu():
    """Show the main menu without auto-starting the bot"""
    print("\n" + "=" * 100)
    print("🚀 SOLANA TRADING BOT")
    print("=" * 100)
    print("\n=== Main Menu ===")
    print("1. Start Bot (Initialize and show full menu)")
    print("2. Exit")

    while True:
        try:
            choice = input("\nEnter your choice (1-2): ").strip()

            if choice == "1":
                print("🔧 Initializing bot...")
                # Import and run the main function
                import asyncio
                from main import main
                asyncio.run(main())
                break
            elif choice == "2":
                print("👋 Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1 or 2.")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
            break

class CLIInterface:
    """Handles the command-line interface for the trading bot"""

    def __init__(self, bot: BotController):
        self.bot = bot
        self._should_exit = False

    async def start(self):
        """Start the CLI interface"""
        try:
            await self.run_menu()
        except KeyboardInterrupt:
            print("\nStopping CLI interface...")
        except Exception as e:
            print(f"Error in CLI interface: {e}")
        finally:
            # Ensure cleanup runs even if run_menu raises an exception other than KeyboardInterrupt
            if self.bot.status != "STOPPED": # Avoid double cleanup if already stopped
                 await self.bot.stop_trading() # Use stop_trading which includes cleanup

    async def run_menu(self):
        """Run the main CLI menu loop asynchronously"""
        loop = asyncio.get_running_loop() # Get the current event loop
        while True:
            self._display_menu()

            # Use run_in_executor to run the blocking input() in a separate thread
            prompt_message = "\nEnter your choice (1-45): "
            # Use functools.partial to pass the prompt message to input
            choice_task = loop.run_in_executor(None, functools.partial(input, prompt_message))

            try:
                # Wait for the user input (running in executor) without blocking the loop
                choice = await choice_task
            except EOFError: # Handle case where input stream is closed
                print("\nInput stream closed. Exiting.")
                await self._handle_choice(35) # Treat as exit
                break
            except KeyboardInterrupt: # Allow Ctrl+C to exit cleanly during input
                print("\nExiting...")
                await self._handle_choice(35) # Treat as exit
                break
            except RuntimeError as e:
                 if "Cannot run input()" in str(e):
                      print("\nError: Cannot run input() in this environment. Exiting.")
                      await self._handle_choice(35) # Treat as exit
                      break
                 else:
                      print(f"\nRuntime Error: {e}")
                      await self._handle_choice(35) # Treat as exit
                      break


            try:
                choice_num = int(choice)
                if choice_num < 1 or choice_num > 45:
                    print("Invalid choice. Please enter a number between 1 and 45.")
                    continue

                await self._handle_choice(choice_num)

                # Check if we should exit (Option 42)
                if self._should_exit:
                    print("Exiting CLI loop...")
                    break

                if choice_num == 45:  # Updated exit option
                    print("Exiting CLI loop...")
                    break

            except ValueError:
                print("Invalid input. Please enter a number.")
            except Exception as e:
                # Log the exception for debugging
                import logging
                logger = logging.getLogger(__name__) # Get a logger instance
                logger.error(f"Error processing choice '{choice}': {e}", exc_info=True)
                print(f"Error processing choice: {e}")

    def _display_menu(self):
        """Display the main menu options with a status summary at the top"""
        # Get current state information
        status = self.bot.status # Directly use status from bot controller
        mode = self.bot.run_mode
        strategy = self.bot.get_current_strategy() or "default"

        # Get real wallet balance for display
        capital = self.bot.get_starting_balance()  # Starting trading balance
        trade_size = self.bot.get_sol_per_trade()

        # Get real wallet balance using synchronous method
        balance = self._get_real_wallet_balance_sync()

        positions_count = len(self.bot.get_positions())
        total_trades = self.bot.get_total_trades()
        win_rate = self.bot.get_win_rate()
        total_profit = self.bot.get_total_profit()

        # Display a special notice if the bot is running
        if status == "RUNNING":
            print("\n" + "!" * 100)
            print("! BOT IS CURRENTLY RUNNING IN THE BACKGROUND")
            print("! You can continue using the menu while the bot processes signals")
            print("! Use option 3 to stop the bot when needed")
            print("!" * 100)

        # Get Telegram status
        telegram_status = "CONNECTED"
        if not self.bot.is_telegram_connected():
            # Check if the session file is corrupted
            session_file = f"{self.bot.signal_handler.session_path}.session"
            if os.path.exists(session_file):
                try:
                    # Try to open the file as SQLite database
                    import sqlite3
                    conn = sqlite3.connect(session_file)
                    conn.close()
                except sqlite3.Error:
                    # If we get an error, the file is corrupted
                    telegram_status = "ERROR: file is not a database"
                    # Mark the session as corrupted in the session status file
                    try:
                        import json
                        from datetime import datetime
                        session_status_file = os.path.join("telegram_logs", "session_status.json")
                        os.makedirs("telegram_logs", exist_ok=True)

                        session_status = {}
                        if os.path.exists(session_status_file):
                            try:
                                with open(session_status_file, 'r', encoding='utf-8') as f:
                                    session_status = json.load(f)
                            except:
                                session_status = {}

                        session_status = {
                            'status': 'corrupted',
                            'last_error': datetime.now().isoformat(),
                            'error_message': 'file is not a database'
                        }

                        with open(session_status_file, 'w', encoding='utf-8') as f:
                            json.dump(session_status, f, indent=2)
                    except:
                        pass
                else:
                    telegram_status = "DISCONNECTED"
            else:
                telegram_status = "DISCONNECTED"

        # Get signal queue size
        signal_queue_size = self.bot.get_signal_queue_size()

        # Get active agents
        active_agents = self.bot.get_active_agents_count()
        max_agents = self.bot.get_max_agents()

        # Display status summary with network mode (real trading only)
        network_mode = getattr(self.bot, 'network_mode', 'MAINNET')
        mode_display = f"{mode} ({network_mode})"

        print("\n" + "=" * 100)

        # ENHANCED: Crystal clear wallet and balance display (real trading only)
        # Get active wallet info for clear display (synchronous)
        active_wallet_name = self._get_active_wallet_name_sync()

        print(f"🎯 Active Wallet: {active_wallet_name}")
        print(f"💰 Trading Capital: {capital:.4f} SOL | Available: {balance:.4f} SOL")
        print(f"📊 Trade Size: {trade_size:.4f} SOL | Strategy: {strategy} | Status: {status}")
        print("⚠️  WARNING: Using REAL money on mainnet!")

        print(f"Mode: {mode_display}")
        print(f"Strategy: {strategy}")
        print(f"Telegram: {telegram_status}")
        print(f"Signal Queue Size: {signal_queue_size}")
        print(f"Simulated Positions: {positions_count}")
        print(f"Active Agents: {active_agents}/{max_agents}")

        # ENHANCED: Clear guidance for wallet management (real trading only)
        print("💡 Use option 39 for all wallet balances | Option 40 to switch wallet")

        print(f"Total Trades: {total_trades}")
        print(f"Winrate: {win_rate}%")
        print(f"Total Profit: {total_profit:.4f} SOL")
        print("=" * 100)

        # Display menu options
        print("\n=== Solana Trading Bot Menu ===")
        print("1. Start Trading (Real Mode)")
        print("2. Stop Trading")
        print("3. Enable Signal Processing")
        print("4. Disable Signal Processing")
        print("5. View Positions")
        print("6. Send Test Message to Info Group")
        print("7. View Bot Status")
        print("8. Sell All Positions")
        print("9. Set Trading Strategy")
        print("10. Set Total SOL")
        print("11. Set SOL per Trade")
        print("12. Authorize Telegram")
        print("13. View Signal Status")
        print("14. Check API Status")
        print("15. Clear API Cache")
        print("16. Reset API Stats")
        print("17. Display Pump Tokens")
        print("18. Display Live SOL Price")
        print("19. Display API Stats")
        print("20. View Live Stats")
        print("21. Analyze Token Metrics")
        print("22. Check WebSocket Status")
        print("23. View Trade History")
        print("24. View Error Log")
        print("25. View Performance Summary")
        print("26. View High Performing Tokens")
        print("27. Fix Corrupted State/Telegram Session")
        print("28. Display Pump.fun Tokens")
        print("29. Test Telegram Connection")
        print("30. View Pipeline Status")
        print("31. View Signal Debug Logs")
        print("32. View Sell Notifications")
        print("33. Force New Telegram Authentication")
        print("34. Clear Bot State")
        print("35. Fix State & Balance Issues (CRITICAL FIX)")
        print("36. Check/Restart Position Monitoring")
        print("37. Add Wallet")
        print("38. List Wallets")
        print("39. Remove Wallet")
        print("40. Check All Wallet Balances")
        print("41. Switch Active Trading Wallet")
        print("42. Reload Configuration")
        print("43. Exit Bot")

    async def _handle_choice(self, choice: int):
        """Handle menu choice selection"""
        if choice == 1:
            # Start trading with proper error handling and monitoring
            try:
                print("🔧 Starting trading in real mode...")
                # Start trading and wait for initial setup to complete
                startup_task = asyncio.create_task(self.bot.start_trading())

                # Wait a short time for critical components to start
                await asyncio.sleep(2.0)

                # Check if startup completed or failed
                if startup_task.done():
                    try:
                        await startup_task  # This will raise any exception that occurred
                        print("✅ Trading started successfully in real mode.")
                    except Exception as startup_error:
                        print(f"❌ Error during startup: {startup_error}")
                        return
                else:
                    print("✅ Trading startup in progress. CLI will remain responsive.")

                # Ensure position monitoring is running
                await self._ensure_position_monitoring()

                print("✅ Trading started in real mode. CLI will remain responsive.")
                print("You can continue using the menu while the bot is running.")
                print("Use option 2 to stop trading when needed.")
            except Exception as e:
                print(f"Error starting trading: {e}")
        elif choice == 2:
            await self.bot.stop_trading()
        elif choice == 3:
            # Enable signal processing
            self.bot.signal_handler.enable_signal_processing()
        elif choice == 4:
            # Disable signal processing
            self.bot.signal_handler.disable_signal_processing()
        elif choice == 5:
            await self.bot.view_positions()
        elif choice == 6:
            message = input("Enter test message: ")
            await self.bot.send_test_message(message)
        elif choice == 7:
            await self.bot.view_bot_status()
        elif choice == 8:
            await self.bot.sell_all_positions()
        elif choice == 9:
            print("\nAvailable Strategies:")
            print("[1] SAFE - 15% take profit, -30% stop loss")
            print("[2] AGGRESSIVE - 20% take profit, -40% stop loss")
            print("[3] ULTRA_CONSERVATIVE - 10% take profit, -20% stop loss")
            print("[4] CUSTOM - Use values from config.json")

            strat_choice = input("\nSelect strategy (1-4): ").strip()

            if strat_choice == "1":
                await self.bot.set_strategy("SAFE")
            elif strat_choice == "2":
                await self.bot.set_strategy("AGGRESSIVE")
            elif strat_choice == "3":
                await self.bot.set_strategy("ULTRA_CONSERVATIVE")
            elif strat_choice == "4":
                await self.bot.set_strategy("CUSTOM")
            else:
                print("Invalid selection. Please choose a number between 1 and 4.")
        elif choice == 10:
            await self.bot.set_total_sol_interactive()
        elif choice == 11:
            await self.bot.set_sol_per_trade_interactive()
        elif choice == 12:
            await self.bot.authorize_telegram()
        elif choice == 13:
            # Call the updated view_signal_status method to show real signals
            await self.bot.view_signal_status()
        elif choice == 14:
            await self.bot.check_api_status()
        elif choice == 15:
            minutes = input("Clear cache older than (minutes): ")
            try: await self.bot.clear_api_cache(int(minutes))
            except ValueError: print("Invalid number of minutes.")
        elif choice == 16:
            await self.bot.reset_api_stats()
        elif choice == 17:
            await self.bot.display_pump_tokens()
        elif choice == 18:
            await self.bot.display_live_sol_price()
        elif choice == 19:
            await self.bot.display_api_stats()
        elif choice == 20:
            await self.bot.view_live_stats()
        elif choice == 21:
            token = input("Enter token address: ")
            await self.bot.analyze_token_metrics(token)
        elif choice == 22:
            await self.bot.check_websocket_status()
        elif choice == 23:
            self._view_trade_history()
        elif choice == 24:
            self._view_error_log()
        elif choice == 25:
            self._view_performance_summary()
        elif choice == 26:
            self._view_high_performing_tokens()
        elif choice == 27:
            await self._fix_corrupted_state()
        elif choice == 28:
            await self.bot.display_pump_fun_tokens()
        elif choice == 29:
            await self._test_telegram_connection()
        elif choice == 30:
            await self._view_pipeline_status()
        elif choice == 31:
            self._view_signal_debug_logs()
        elif choice == 32:
            self._view_sell_notifications()
        elif choice == 33:
            await self._force_new_telegram_auth()
        elif choice == 34:
            await self._clear_bot_state()
        elif choice == 35:
            await self._fix_state_and_balance_issues()
        elif choice == 36:
            await self._check_restart_position_monitoring()
        elif choice == 37:
            await self._add_wallet()
        elif choice == 38:
            await self._list_wallets()
        elif choice == 39:
            await self._remove_wallet()
        elif choice == 40:
            await self._check_wallet_balance()
        elif choice == 41:
            await self._switch_active_trading_wallet()
        elif choice == 42:
            await self._reload_configuration()
        elif choice == 43:
            print("🛑 Shutting down bot...")
            print("Stopping all trading activities...")

            # Stop trading first
            await self.bot.stop_trading()

            # Cleanup bot resources
            await self.bot.cleanup()

            print("✅ Bot shutdown complete. Exiting...")

            # Set a flag to exit the main loop
            self._should_exit = True

            # Force exit the entire process
            import os
            print("🚪 Forcing process exit...")
            os._exit(0)  # Force exit without cleanup (cleanup already done above)

    def _get_active_wallet_name_sync(self):
        """Get the active wallet name synchronously"""
        try:
            # Try to get from unified trader first
            if hasattr(self.bot, 'unified_trader') and self.bot.unified_trader:
                if hasattr(self.bot.unified_trader, 'pumpportal_trader') and self.bot.unified_trader.pumpportal_trader:
                    if self.bot.unified_trader.pumpportal_trader.default_wallet:
                        return self.bot.unified_trader.pumpportal_trader.default_wallet['name']
                if hasattr(self.bot.unified_trader, 'helius_trader') and self.bot.unified_trader.helius_trader:
                    if self.bot.unified_trader.helius_trader.default_wallet:
                        return self.bot.unified_trader.helius_trader.default_wallet['name']

            # Fallback to legacy PumpPortal
            if hasattr(self.bot, 'pumpportal_trader') and self.bot.pumpportal_trader:
                if self.bot.pumpportal_trader.default_wallet:
                    return self.bot.pumpportal_trader.default_wallet['name']

            # Fallback to config
            wallet_settings = self.bot.config.get_section('wallet_settings')
            return wallet_settings.get('default_wallet_name', 'Unknown')

        except Exception as e:
            logger.debug(f"Error getting active wallet name: {e}")
            return "Unknown"

    def _get_real_wallet_balance_sync(self) -> float:
        """Get the real wallet balance synchronously"""
        try:
            # Get the active wallet name
            active_wallet_name = self._get_active_wallet_name_sync()

            # Get wallet info from wallet manager
            from wallet_manager import WalletManager
            wallet_manager = WalletManager()
            wallet_info = wallet_manager.get_wallet_info(active_wallet_name)

            if not wallet_info:
                logger.debug(f"No wallet info found for {active_wallet_name}")
                return self.bot.get_trading_balance()  # Fallback to state balance

            # Use synchronous HTTP request to get balance
            import requests
            from dotenv import load_dotenv
            import os

            load_dotenv(override=True)
            api_key = os.getenv('HELIUS_API_KEY')

            if not api_key:
                logger.debug("No HELIUS_API_KEY found")
                return self.bot.get_trading_balance()

            rpc_url = f"https://mainnet.helius-rpc.com/?api-key={api_key}"

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [wallet_info['public_key']]
            }

            response = requests.post(rpc_url, json=payload, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if 'result' in data and 'value' in data['result']:
                    lamports = data['result']['value']
                    return lamports / 1_000_000_000  # Convert to SOL

            return self.bot.get_trading_balance()  # Fallback

        except Exception as e:
            logger.debug(f"Error getting real wallet balance: {e}")
            return self.bot.get_trading_balance()  # Fallback to state balance

    async def _get_wallet_balance_by_name(self, wallet_name: str) -> float:
        """Get balance for a specific wallet by name"""
        try:
            # Create a temporary wallet manager to get wallet info
            wallet_manager = WalletManager()
            wallet_info = wallet_manager.get_wallet_info(wallet_name)

            if not wallet_info:
                return 0.0

            # Use the appropriate trader to check balance
            if hasattr(self.bot, 'unified_trader') and self.bot.unified_trader:
                # For unified trader, we need to temporarily switch wallet or use direct RPC
                # For now, use direct RPC call with the wallet's public key
                return await self._get_balance_by_public_key(wallet_info['public_key'])
            elif hasattr(self.bot, 'pumpportal_trader') and self.bot.pumpportal_trader:
                # Use direct RPC call for balance checking
                return await self._get_balance_by_public_key(wallet_info['public_key'])

            return 0.0

        except Exception as e:
            logger.debug(f"Error getting balance for wallet {wallet_name}: {e}")
            return 0.0

    async def _get_balance_by_public_key(self, public_key: str) -> float:
        """Get SOL balance by public key using RPC - network aware"""
        try:
            import aiohttp

            # Always use mainnet (devnet disabled)
            from dotenv import load_dotenv
            import os
            load_dotenv(override=True)
            api_key = os.getenv('HELIUS_API_KEY')

            rpc_url = f"https://mainnet.helius-rpc.com/?api-key={api_key}"

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [public_key]
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(rpc_url, json=payload, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'result' in data and 'value' in data['result']:
                            lamports = data['result']['value']
                            return lamports / 1_000_000_000  # Convert to SOL

            return 0.0

        except Exception as e:
            logger.debug(f"Error getting balance by public key: {e}")
            return 0.0

    async def _switch_active_trading_wallet(self):
        """Switch the active trading wallet"""
        print("\n🔄 SWITCH ACTIVE TRADING WALLET")
        print("=" * 50)

        try:
            # Get all available wallets
            wallet_manager = WalletManager()
            wallets = wallet_manager.list_wallets()

            if not wallets:
                print("❌ No wallets found in .env file")
                print("💡 Use option 36 to add a wallet")
                return

            # Show current active wallet
            current_active = self._get_active_wallet_name_sync()
            print(f"🎯 Current Active Wallet: {current_active}")

            # Show available wallets
            print(f"\n💼 Available Wallets:")
            for i, wallet in enumerate(wallets, 1):
                is_current = wallet['name'].upper() == current_active.upper()
                status = " ⭐ (Currently Active)" if is_current else ""
                print(f"{i}. {wallet['name']}{status}")
                print(f"   📍 Public Key: {wallet['public_key'][:8]}...{wallet['public_key'][-8:]}")

            # Get user choice
            try:
                choice = input(f"\nSelect wallet to activate (1-{len(wallets)}) or 'c' to cancel: ").strip()

                if choice.lower() == 'c':
                    print("❌ Operation cancelled")
                    return

                wallet_index = int(choice) - 1
                if wallet_index < 0 or wallet_index >= len(wallets):
                    print("❌ Invalid selection")
                    return

                selected_wallet = wallets[wallet_index]
                wallet_name = selected_wallet['name']

                # Confirm the switch
                if wallet_name.upper() == current_active.upper():
                    print(f"💡 {wallet_name} is already the active wallet")
                    return

                confirm = input(f"🔄 Switch to wallet '{wallet_name}'? (yes/no): ").strip().lower()
                if confirm not in ['yes', 'y']:
                    print("❌ Operation cancelled")
                    return

                # Perform the wallet switch
                success = await self._perform_wallet_switch(wallet_name)

                if success:
                    print(f"✅ Successfully switched to wallet: {wallet_name}")
                    print(f"🎯 All future trades will use: {wallet_name}")
                    print("💡 Restart trading to ensure the new wallet is used")
                else:
                    print(f"❌ Failed to switch to wallet: {wallet_name}")

            except ValueError:
                print("❌ Invalid input. Please enter a number.")
            except Exception as e:
                print(f"❌ Error during wallet selection: {e}")

        except Exception as e:
            print(f"❌ Error switching wallet: {e}")

        # CRITICAL FIX: Add explicit return to continue CLI loop
        return

    async def _perform_wallet_switch(self, wallet_name: str) -> bool:
        """Perform the actual wallet switch"""
        try:
            # Update the configuration in memory
            wallet_settings = self.bot.config.get_section('wallet_settings')
            wallet_settings['default_wallet_name'] = wallet_name.upper()

            # Update the config file directly
            import json
            config_path = self.bot.config.config_path
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Update wallet settings
            if 'wallet_settings' not in config_data:
                config_data['wallet_settings'] = {}
            config_data['wallet_settings']['default_wallet_name'] = wallet_name.upper()

            # Save the updated configuration
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)

            # Reload traders with new wallet
            if hasattr(self.bot, 'unified_trader') and self.bot.unified_trader:
                # Reinitialize unified trader to pick up new wallet
                if hasattr(self.bot, '_ensure_unified_trader_initialized'):
                    await self.bot._ensure_unified_trader_initialized()

            if hasattr(self.bot, 'pumpportal_trader') and self.bot.pumpportal_trader:
                # Reload the default wallet in PumpPortal trader
                if hasattr(self.bot.pumpportal_trader, '_load_default_wallet'):
                    self.bot.pumpportal_trader.default_wallet = self.bot.pumpportal_trader._load_default_wallet()

            if hasattr(self.bot, 'helius_trader') and self.bot.helius_trader:
                # Reload the default wallet in Helius trader
                if hasattr(self.bot.helius_trader, '_load_default_wallet'):
                    self.bot.helius_trader.default_wallet = self.bot.helius_trader._load_default_wallet()

            return True

        except Exception as e:
            logger.error(f"Error performing wallet switch: {e}")
            return False

    async def _add_wallet(self):
        """Add a new wallet to .env file"""
        print("\n🔐 ADD WALLET TO .ENV")
        print("=" * 30)

        wallet_manager = WalletManager()

        # Get wallet details
        wallet_name = input("Enter wallet name: ").strip()
        if not wallet_name:
            print("❌ Wallet name required")
            return

        private_key = input("Enter private key (base58): ").strip()
        if not private_key:
            print("❌ Private key required")
            return

        success = wallet_manager.add_wallet(wallet_name, private_key)
        if success:
            print("✅ Wallet added successfully!")
        else:
            print("❌ Failed to add wallet")

    async def _list_wallets(self):
        """List all wallets from .env file"""
        print("\n💼 WALLET LIST")
        print("=" * 50)

        wallet_manager = WalletManager()
        wallets = wallet_manager.list_wallets()

        if not wallets:
            print("❌ No wallets found in .env file")
            print("💡 Use 'Add Wallet' option to add a wallet")
            return

        for i, wallet in enumerate(wallets, 1):
            print(f"\n{i}. {wallet['name']}")
            print(f"   📍 Public Key: {wallet['public_key']}")
            print(f"   📅 Created: {wallet['created_at']}")
            print(f"   📊 Trades: {wallet['trades_count']}")

    async def _remove_wallet(self):
        """Remove a wallet from .env file"""
        print("\n🗑️ REMOVE WALLET")
        print("=" * 30)

        wallet_manager = WalletManager()
        wallets = wallet_manager.list_wallets()

        if not wallets:
            print("❌ No wallets found")
            return

        # Show available wallets
        print("\nAvailable wallets:")
        for i, wallet in enumerate(wallets, 1):
            print(f"{i}. {wallet['name']} ({wallet['public_key'][:8]}...)")

        # Get wallet to remove
        try:
            choice = input("\nEnter wallet name to remove: ").strip()
            if not choice:
                print("❌ No wallet selected")
                return

            # Confirm removal
            confirm = input(f"⚠️ Remove wallet '{choice}'? (yes/no): ").strip().lower()
            if confirm not in ['yes', 'y']:
                print("❌ Removal cancelled")
                return

            success = wallet_manager.remove_wallet(choice)
            if success:
                print("✅ Wallet removed successfully!")
            else:
                print("❌ Failed to remove wallet")

        except Exception as e:
            print(f"❌ Error: {e}")

    async def _check_wallet_balance(self):
        """Check SOL balance of ALL wallets with clear active wallet indication"""
        print("\n💰 ALL WALLET BALANCES")
        print("=" * 50)

        try:
            # Show current network mode
            current_mode = getattr(self.bot, 'network_mode', 'MAINNET')
            print(f"🌐 Network: {current_mode}")

            # Simulation mode removed - only real balances

            # Get all wallets from .env
            wallet_manager = WalletManager()
            wallets = wallet_manager.list_wallets()

            if not wallets:
                print("❌ No wallets found in .env file")
                print("💡 Use option 36 to add a wallet")
                return

            # Get active wallet name
            active_wallet_name = self._get_active_wallet_name_sync()

            print(f"\n🏦 PORTFOLIO OVERVIEW:")
            total_portfolio_value = 0.0

            for wallet in wallets:
                wallet_name = wallet['name']

                # Check if this is the active wallet
                is_active = wallet_name.upper() == active_wallet_name.upper()
                status_icon = "⭐ (Active)" if is_active else ""

                try:
                    # Get balance for this specific wallet
                    balance = await self._get_wallet_balance_by_name(wallet_name)
                    usd_value = balance * 200  # Estimated USD value
                    total_portfolio_value += balance

                    print(f"• {wallet_name}: {balance:.4f} SOL (~${usd_value:.2f}) {status_icon}")

                except Exception as e:
                    print(f"• {wallet_name}: ❌ Error getting balance - {e}")

            print(f"\n📊 Total Portfolio: {total_portfolio_value:.4f} SOL (~${total_portfolio_value * 200:.2f})")

            # Show trading info
            trading_balance = self.bot.get_trading_balance()
            print(f"💰 Trading Budget: {trading_balance:.4f} SOL (Virtual allocation)")
            print(f"🎯 Active for Trading: {active_wallet_name}")

        except Exception as e:
            print(f"❌ Error checking wallet balances: {e}")

    # Network mode switching removed - mainnet only mode

    def _view_trade_history(self):
        """View trade history from CSV"""
        try:
            with open('trade_log.csv', 'r') as f:
                print("\nTrade History:")
                print(f.read())
        except FileNotFoundError:
            print("No trade history found.")

    def _view_error_log(self):
        """View error log from CSV"""
        try:
            with open('error_log.csv', 'r') as f:
                print("\nError Log:")
                print(f.read())
        except FileNotFoundError:
            print("No error log found.")

    def _view_performance_summary(self):
        """View performance summary from CSV"""
        try:
            with open('performance_summary.csv', 'r') as f:
                print("\nPerformance Summary:")
                print(f.read())
        except FileNotFoundError:
            print("No performance summary found.")

    def _view_high_performing_tokens(self):
        """View high performing tokens from CSV"""
        try:
            with open('high_performing_tokens.csv', 'r') as f:
                print("\nHigh Performing Tokens:")
                print(f.read())
        except FileNotFoundError:
            print("No high performing tokens data found.")

    # Removed _toggle_simulated_signals method - using only real signals

    async def _fix_corrupted_state(self):
        """Fix corrupted state or Telegram session files"""
        print("\n=== Fixing Telegram Session ===")
        print("WARNING: This will delete your Telegram session file.")
        print("You will need to re-authorize Telegram after this operation.")

        response = input("\nAre you sure you want to proceed? (yes/no): ")
        if response.lower() not in ["yes", "y"]:
            print("Operation cancelled.")
            return

        # Get the session file path
        session_file = f"{self.bot.signal_handler.session_path}.session"

        # Delete the session file if it exists
        if os.path.exists(session_file):
            try:
                os.remove(session_file)
                print(f"Session file {session_file} deleted successfully.")
            except Exception as e:
                print(f"Error deleting session file: {e}")
        else:
            print("No session file found.")

        # Reset the last connect attempt time in signal handler
        self.bot.signal_handler._last_connect_attempt = 0

        # Reset the authorization cooldown timer in bot_controller
        if hasattr(self.bot, '_last_auth_attempt'):
            self.bot._last_auth_attempt = 0
            print("Reset authorization cooldown timer.")

        # Disconnect the client if it exists
        if self.bot.signal_handler.client:
            try:
                await self.bot.signal_handler.client.disconnect()
                self.bot.signal_handler.client = None
                print("Telegram client disconnected and reset.")
            except Exception as e:
                print(f"Error disconnecting client: {e}")

        print("\nTelegram session has been reset. Please use option 12 to authorize again.")

    async def _test_telegram_connection(self):
        """Test Telegram connection with detailed diagnostics"""
        print("\n=== Testing Telegram Connection ===")

        # Check if client exists
        if not self.bot.signal_handler.client:
            print("❌ Telegram client not initialized. Please authorize Telegram first (option 12).")
            return

        # Check if client is connected
        try:
            is_connected = self.bot.signal_handler.client.is_connected
            print(f"Client connection status: {'✅ Connected' if is_connected else '❌ Disconnected'}")

            if not is_connected:
                print("Attempting to connect...")
                await self.bot.signal_handler.client.connect()
                is_connected = self.bot.signal_handler.client.is_connected
                print(f"Connection attempt result: {'✅ Connected' if is_connected else '❌ Failed'}")
        except Exception as e:
            print(f"❌ Error checking connection status: {e}")
            return

        # Check if client is authorized
        try:
            is_authorized = await self.bot.signal_handler.client.is_user_authorized()
            print(f"Authorization status: {'✅ Authorized' if is_authorized else '❌ Not authorized'}")

            if not is_authorized:
                print("Please use option 12 to authorize Telegram.")
                return
        except Exception as e:
            print(f"❌ Error checking authorization status: {e}")
            return

        # Get info channel ID
        info_channel_id_str = self.bot.signal_handler.telegram_settings.get('bot_info_channel_id')
        if not info_channel_id_str:
            print("❌ 'bot_info_channel_id' not set in config.")
            return

        # Try to parse the channel ID
        try:
            info_channel_id_str = str(info_channel_id_str).strip('"\'')
            info_channel_id = int(info_channel_id_str)
            print(f"Info channel ID: {info_channel_id}")
        except ValueError:
            print(f"❌ Invalid bot_info_channel_id in config: '{info_channel_id_str}'. Must be an integer.")
            return

        # Try to resolve the entity
        try:
            print("Attempting to resolve channel entity...")
            entity = await self.bot.signal_handler.client.get_entity(info_channel_id)

            # Log entity details
            entity_type = type(entity).__name__
            entity_id = getattr(entity, 'id', 'unknown')
            entity_title = getattr(entity, 'title', 'unknown')
            print(f"✅ Successfully resolved entity: Type={entity_type}, ID={entity_id}, Title={entity_title}")
        except Exception as e:
            print(f"❌ Error resolving entity: {e}")

        # Send test message
        print("\nSending test message...")
        success = await self.bot.send_test_message("Test message from Telegram connection test")

        if success:
            print("✅ Test message sent successfully!")
            print("✅ Telegram connection is working properly.")
        else:
            print("❌ Failed to send test message.")
            print("Please check your Telegram configuration and try again.")

    async def _view_pipeline_status(self):
        """View the status of the parallel pipeline architecture"""
        print("\n=== Parallel Pipeline Status ===")

        # Get pipeline status
        pipeline_status = self.bot.get_pipeline_status()

        if not pipeline_status:
            print("Parallel pipeline is not initialized. Start trading to initialize the pipeline.")
            return

        # Display pipeline status
        print(f"Analysis Workers: {pipeline_status.get('analysis_workers', 0)}")
        print(f"Execution Workers: {pipeline_status.get('execution_workers', 0)}")
        print(f"Analysis Queue Size: {pipeline_status.get('analysis_queue_size', 0)}")
        print(f"Buy Queue Size: {pipeline_status.get('buy_queue_size', 0)}")
        print(f"Sell Queue Size: {pipeline_status.get('sell_queue_size', 0)}")
        print(f"Analysis Worker Running: {pipeline_status.get('analysis_worker_running', False)}")
        print(f"Execution Queue Running: {pipeline_status.get('execution_queue_running', False)}")

        # Get analysis worker stats
        analysis_stats = self.bot.get_analysis_worker_stats()

        if analysis_stats:
            print("\n=== Analysis Worker Stats ===")
            print(f"Total Processed: {analysis_stats.get('total_processed', 0)}")
            print(f"Successful Analyses: {analysis_stats.get('successful_analyses', 0)}")
            print(f"Failed Analyses: {analysis_stats.get('failed_analyses', 0)}")
            print(f"Average Processing Time: {analysis_stats.get('avg_processing_time', 0):.2f}s")

        # Get execution queue stats
        execution_stats = self.bot.get_execution_queue_stats()

        if execution_stats:
            print("\n=== Execution Queue Stats ===")
            print(f"Total Buys Processed: {execution_stats.get('total_buys_processed', 0)}")
            print(f"Successful Buys: {execution_stats.get('successful_buys', 0)}")
            print(f"Failed Buys: {execution_stats.get('failed_buys', 0)}")
            print(f"Average Buy Time: {execution_stats.get('avg_buy_time', 0):.2f}s")
            print(f"Total Sells Processed: {execution_stats.get('total_sells_processed', 0)}")
            print(f"Successful Sells: {execution_stats.get('successful_sells', 0)}")
            print(f"Failed Sells: {execution_stats.get('failed_sells', 0)}")
            print(f"Average Sell Time: {execution_stats.get('avg_sell_time', 0):.2f}s")

    def _view_signal_debug_logs(self):
        """View the signal debug logs"""
        print("\n=== Signal Debug Logs ===")

        # Find the most recent signal log file
        import glob
        import os

        signal_log_files = glob.glob('logs/signals/signal_*.log')
        if not signal_log_files:
            print("No signal log files found.")
            return

        # Sort by modification time (newest first)
        signal_log_files.sort(key=os.path.getmtime, reverse=True)
        latest_log = signal_log_files[0]

        print(f"Displaying most recent signal log: {latest_log}")
        print("\nLast 50 lines of signal log:")
        print("-" * 80)

        try:
            # Read the last 50 lines of the log file
            with open(latest_log, 'r') as f:
                lines = f.readlines()
                for line in lines[-50:]:
                    print(line.strip())

            # Check for detailed signal logs
            detail_logs = glob.glob('logs/signals/details/signal_*.txt')
            if detail_logs:
                detail_logs.sort(key=os.path.getmtime, reverse=True)
                latest_detail = detail_logs[0]

                print("\n\nMost recent detailed signal:")
                print("-" * 80)

                with open(latest_detail, 'r') as f:
                    print(f.read())

            print("\nTo view raw Telegram messages, check logs/raw_messages/ directory")

        except Exception as e:
            print(f"Error reading log file: {e}")

    async def _fix_state_and_balance_issues(self):
        """CRITICAL FIX: Fix state synchronization and balance discrepancy issues"""
        print("\n=== CRITICAL STATE & BALANCE FIX ===")
        print("🔧 This will fix:")
        print("   • Duplicate position tracking")
        print("   • Balance calculation errors")
        print("   • Trade statistics corruption")
        print("   • State file synchronization")
        print("   • Position monitoring issues")

        confirm = input("\nProceed with critical fix? (y/n): ")

        if confirm.lower() not in ["y", "yes"]:
            print("Operation cancelled.")
            return

        try:
            print("\n🔧 EXECUTING CRITICAL STATE FIX...")

            # Execute the state sync and cleanup
            result = self.bot.state.force_state_sync_and_cleanup()

            print(f"\n🎉 CRITICAL FIX COMPLETED SUCCESSFULLY!")
            print(f"📊 RESULTS:")
            print(f"   ✅ Duplicate positions removed: {result['duplicates_removed']}")
            print(f"   ✅ Open positions tracked: {result['open_positions']}")
            print(f"   ✅ Available SOL balance: {result['available_sol']:.4f}")
            print(f"   ✅ Total completed trades: {result['total_trades']}")
            print(f"   ✅ Win rate: {result['win_rate']:.1f}%")

            # Send notification to Telegram about the fix
            if hasattr(self.bot, 'signal_handler') and self.bot.signal_handler:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                notification_message = (
                    f"🔧 CRITICAL STATE FIX COMPLETED — {timestamp}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"✅ STATE SYNCHRONIZATION SUCCESSFUL\n\n"
                    f"📊 Fix Results:\n"
                    f" • Duplicate positions removed: {result['duplicates_removed']}\n"
                    f" • Open positions tracked: {result['open_positions']}\n"
                    f" • Available SOL: {result['available_sol']:.4f}\n"
                    f" • Completed trades: {result['total_trades']}\n"
                    f" • Win rate: {result['win_rate']:.1f}%\n\n"
                    f"🎯 Issues Fixed:\n"
                    f" • Balance calculation errors\n"
                    f" • Duplicate position tracking\n"
                    f" • Trade statistics corruption\n"
                    f" • State file synchronization\n\n"
                    f"✅ Bot is now operating with correct data!"
                )
                try:
                    await self.bot.signal_handler.send_info_message(notification_message)
                    print("✅ Fix notification sent to Telegram.")
                except Exception as e:
                    print(f"❌ Error sending notification: {e}")

            print(f"\n🚀 RECOMMENDATIONS:")
            print(f"   • Check option 4 (View Positions) to verify open positions")
            print(f"   • Check option 6 (View Bot Status) to verify balance")
            print(f"   • Monitor notifications for correct balance display")
            print(f"   • The bot will now operate with accurate data")

        except Exception as e:
            print(f"❌ CRITICAL ERROR during state fix: {e}")
            print(f"Please check logs for detailed error information.")
            import traceback
            traceback.print_exc()

    async def _clear_bot_state(self):
        """Clear the bot's state including positions, signals, and trade history"""
        print("\n=== Clear Bot State ===")
        print("⚠️ WARNING: This will clear all positions, signals, and trade history.")
        print("⚠️ This action cannot be undone.")

        confirm = input("\nAre you sure you want to proceed? (y/n): ")

        if confirm.lower() not in ["y", "yes"]:
            print("Operation cancelled.")
            return

        try:
            # Send notification to Telegram about state being cleared
            if hasattr(self.bot, 'signal_handler') and self.bot.signal_handler:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                notification_message = (
                    f"⚠️ IMPORTANT BOT MESSAGE — {timestamp}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"🔄 BOT STATE RESET\n\n"
                    f"All positions, signals, and trade history have been cleared.\n"
                    f"The bot will start fresh with initial capital settings.\n\n"
                    f"✅ Action: Manual reset via CLI option 33"
                )
                try:
                    await self.bot.signal_handler.send_info_message(notification_message)
                    print("✅ Notification sent to Telegram about state reset.")
                except Exception as e:
                    print(f"❌ Error sending notification to Telegram: {e}")

            # Clear state in StateManager
            if hasattr(self.bot, 'state') and self.bot.state:
                # CRITICAL FIX: Clear ALL state files, not just the default one
                import glob
                import os
                import time

                # Find all session files
                session_files = glob.glob("*.session") + glob.glob("sessions/*.session")

                print(f"🔍 Found {len(session_files)} state files to clear:")
                for session_file in session_files:
                    print(f"   - {session_file}")

                # Clear each state file
                for session_file in session_files:
                    try:
                        if os.path.exists(session_file):
                            # Create backup first
                            backup_path = f"{session_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                            import shutil
                            shutil.copy2(session_file, backup_path)
                            print(f"📁 Backup created: {backup_path}")

                            # Clear the file by writing empty state
                            empty_state = {
                                "positions": {},
                                "trade_history": [],
                                "token_last_trade_time": {},
                                "token_cooldowns": {},
                                "agent_limits": {},
                                "total_profit_sol": 0.0,
                                "total_profit_usd": 0.0,
                                "trades_count": 0,
                                "win_count": 0,
                                "loss_count": 0,
                                "max_drawdown": 0.0,
                                "current_strategy": "default",
                                "starting_sol": 1.2,
                                "available_sol": 1.2,
                                "sol_per_trade": 0.16,
                                "save_timestamp": time.time()
                            }

                            with open(session_file, 'w', encoding='utf-8') as f:
                                import json
                                json.dump(empty_state, f, indent=2)
                            print(f"✅ Cleared state file: {session_file}")
                    except Exception as e:
                        print(f"❌ Error clearing {session_file}: {e}")

                # CRITICAL FIX: Stop any running monitoring loops
                print("⏹️ Stopping monitoring loops...")
                if hasattr(self.bot, '_monitoring_active'):
                    self.bot._monitoring_active = False
                    print("✅ Monitoring loops stopped.")

                # CRITICAL FIX: Clear the bot's in-memory state
                print("🧠 Clearing bot's in-memory state...")
                self.bot.state.clear_state()

                # CRITICAL FIX: Force reload state from cleared file
                print("🔄 Reloading state from cleared file...")
                self.bot.state.load_state()

                # CRITICAL FIX: Reset monitoring state
                if hasattr(self.bot, '_position_update_index'):
                    self.bot._position_update_index = 0
                    print("✅ Reset position update index.")

                # CRITICAL FIX: Clear worker pool caches
                if hasattr(self.bot, 'analysis_worker_pool') and hasattr(self.bot.analysis_worker_pool, 'processed_tokens'):
                    self.bot.analysis_worker_pool.processed_tokens.clear()
                    print("✅ Cleared worker pool processed tokens cache.")

                # CRITICAL FIX: Clear token analyzer cache
                if hasattr(self.bot, 'token_analyzer'):
                    try:
                        await self.bot.token_analyzer.clear_cache()
                        print("✅ Cleared token analyzer cache.")
                    except Exception as e:
                        print(f"⚠️ Warning: Could not clear token analyzer cache: {e}")

                # CRITICAL FIX: Reset any position tracking variables
                if hasattr(self.bot, '_last_positions'):
                    self.bot._last_positions = {}
                    print("✅ Reset last positions tracking.")

                # CRITICAL FIX: Reset sync tracking variables
                if hasattr(self.bot, '_last_sync_error_time'):
                    self.bot._last_sync_error_time = 0
                if hasattr(self.bot, '_sync_error_count'):
                    self.bot._sync_error_count = 0
                if hasattr(self.bot, '_last_balance_update_time'):
                    self.bot._last_balance_update_time = 0
                    print("✅ Reset monitoring sync variables.")

                # Save the cleared state
                self.bot.state.save_state()
                print("✅ Bot state cleared successfully (both file and memory).")

                # Send a balance update to Telegram
                try:
                    await self.bot.send_balance_update(reason="State Reset")
                    print("✅ Balance update sent to Telegram.")
                except Exception as e:
                    print(f"❌ Error sending balance update to Telegram: {e}")
            else:
                print("❌ State manager not available.")

            # Clear signals in SignalHandler
            if hasattr(self.bot, 'signal_handler') and self.bot.signal_handler:
                # Clear signal queue
                try:
                    while not self.bot.signal_handler.signal_queue.empty():
                        try:
                            self.bot.signal_handler.signal_queue.get_nowait()
                            self.bot.signal_handler.signal_queue.task_done()
                        except asyncio.QueueEmpty:
                            break
                    print("✅ Signal queue cleared.")
                except Exception as e:
                    print(f"❌ Error clearing signal queue: {e}")

                # Clear recent signals and processed signals
                self.bot.signal_handler.recent_signals = []
                self.bot.signal_handler.recent_signal_data = {}
                self.bot.signal_handler.processed_signals = set()
                self.bot.signal_handler.recent_signals_info = {}
                print("✅ Recent signals cleared.")
            else:
                print("❌ Signal handler not available.")

            print("\n✅ Bot state has been reset successfully.")
            print("✅ All positions, signals, and trade history have been cleared.")
            print("✅ Telegram notifications have been sent.")

        except Exception as e:
            print(f"❌ Error clearing bot state: {e}")

    async def _force_new_telegram_auth(self):
        """Force a new Telegram authentication by deleting the session string file"""
        print("\n=== Force New Telegram Authentication ===")
        print("WARNING: This will delete your saved Telegram session.")
        print("You will need to enter a new verification code sent to your Telegram app.")

        response = input("\nAre you sure you want to proceed? (yes/no): ")
        if response.lower() not in ["yes", "y"]:
            print("Operation cancelled.")
            return

        try:
            # Get the session string file path from the signal handler
            session_string_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "session_string.json")

            # Delete the session string file if it exists
            if os.path.exists(session_string_file):
                try:
                    os.remove(session_string_file)
                    print(f"Session string file {session_string_file} deleted successfully.")
                except Exception as e:
                    print(f"Error deleting session string file: {e}")
            else:
                print("No session string file found.")

            # Also delete all session files if they exist
            # Get all session files in the current directory
            session_files = [f for f in os.listdir(os.path.dirname(os.path.abspath(__file__))) if f.endswith(".session")]

            if session_files:
                print(f"Found {len(session_files)} session files: {', '.join(session_files)}")

                # Delete each session file
                for session_file in session_files:
                    try:
                        full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), session_file)
                        os.remove(full_path)
                        print(f"Session file {session_file} deleted successfully.")
                    except Exception as e:
                        print(f"Error deleting session file {session_file}: {e}")
            else:
                print("No session files found.")

            # Reset the session string in the signal handler if possible
            if hasattr(self.bot, 'signal_handler') and self.bot.signal_handler:
                self.bot.signal_handler.session_string = None

                # Disconnect the client if it exists
                if hasattr(self.bot.signal_handler, 'client') and self.bot.signal_handler.client:
                    try:
                        await self.bot.signal_handler.client.disconnect()
                        self.bot.signal_handler.client = None
                        print("Telegram client disconnected and reset.")
                    except Exception as e:
                        print(f"Error disconnecting client: {e}")

            print("\nTelegram session has been reset. Please use option 12 to authorize with a new verification code.")
        except Exception as e:
            print(f"Error resetting Telegram session: {e}")

    def _view_sell_notifications(self):
        """View the sell notifications logs"""
        print("\n=== Sell Notifications ===")

        # Find the most recent sell notification log files
        import glob
        import os

        # Check for dedicated sell notification logs
        sell_log_files = glob.glob('logs/sell_notifications/sell_*.txt')
        if sell_log_files:
            # Sort by modification time (newest first)
            sell_log_files.sort(key=os.path.getmtime, reverse=True)

            # Display the 5 most recent sell notifications
            print(f"Found {len(sell_log_files)} sell notification logs. Displaying the 5 most recent:")

            for i, log_file in enumerate(sell_log_files[:5]):
                print(f"\n{i+1}. Sell Notification: {os.path.basename(log_file)}")
                print("-" * 80)

                try:
                    with open(log_file, 'r') as f:
                        print(f.read())
                except Exception as e:
                    print(f"Error reading sell notification file: {e}")
        else:
            print("No dedicated sell notification logs found.")

        # Also check for sell notifications in signal logs
        print("\nChecking for sell notifications in signal logs...")

        # Find signal log files
        signal_log_files = glob.glob('logs/signals/signal_*.log')
        if signal_log_files:
            # Sort by modification time (newest first)
            signal_log_files.sort(key=os.path.getmtime, reverse=True)
            latest_log = signal_log_files[0]

            print(f"Scanning most recent signal log: {latest_log}")
            print("-" * 80)

            try:
                # Read the log file and look for sell notifications
                with open(latest_log, 'r') as f:
                    lines = f.readlines()
                    sell_lines = [line for line in lines if 'SELL NOTIFICATION' in line]

                    if sell_lines:
                        print(f"Found {len(sell_lines)} sell notifications in signal log:")
                        for line in sell_lines[-10:]:  # Show last 10 sell notifications
                            print(line.strip())
                    else:
                        print("No sell notifications found in signal log.")
            except Exception as e:
                print(f"Error reading signal log file: {e}")
        else:
            print("No signal log files found.")

    async def _ensure_position_monitoring(self):
        """Ensure position monitoring is running, start it if not."""
        try:
            # Give position monitoring a moment to start if it's in the process
            await asyncio.sleep(0.5)

            # Check if position monitoring is running
            monitor_running = (hasattr(self.bot, '_monitor_task') and
                             self.bot._monitor_task and
                             not self.bot._monitor_task.done())

            if not monitor_running:
                print("⚠️ Position monitoring not detected, starting fallback monitoring...")
                await self.bot._start_position_monitoring_resilient()
                print("✅ Position monitoring started successfully")
            else:
                print("✅ Position monitoring is already running")
        except Exception as e:
            print(f"❌ Warning: Could not ensure position monitoring: {e}")
            print("⚠️ Positions may not be monitored properly!")

    async def _check_restart_position_monitoring(self):
        """Check position monitoring status and restart if needed."""
        print("\n=== Position Monitoring Status Check ===")

        try:
            # Check if bot is trading
            if not self.bot.is_trading:
                print("❌ Bot is not currently trading. Start trading first to enable position monitoring.")
                return

            # Check monitor task status
            monitor_running = False
            if hasattr(self.bot, '_monitor_task') and self.bot._monitor_task and not self.bot._monitor_task.done():
                monitor_running = True
                print("✅ Position monitoring task is running")
            else:
                print("❌ Position monitoring task is not running")

            # Check for open positions
            open_positions = self.bot.get_positions()
            print(f"📊 Open positions: {len(open_positions)}")

            if open_positions:
                print("⚠️ You have open positions that need monitoring!")
                for token, pos_data in open_positions.items():
                    entry_price = pos_data.get('entry_price', 0)
                    token_amount = pos_data.get('token_amount', 0)
                    print(f"   • {token[:8]}... - {token_amount:.2f} tokens @ ${entry_price:.8f}")

            # Check health checker status
            if hasattr(self.bot, '_monitor_health_check_interval'):
                print("✅ Position monitoring health checker is configured")
            else:
                print("❌ Position monitoring health checker is not configured")

            # Check consecutive errors
            consecutive_errors = getattr(self.bot, '_monitor_consecutive_errors', 0)
            if consecutive_errors > 0:
                print(f"⚠️ Position monitoring has {consecutive_errors} consecutive errors")
            else:
                print("✅ No consecutive monitoring errors")

            # Offer restart options
            if not monitor_running or consecutive_errors > 0:
                print("\n🔧 Position monitoring needs attention!")
                restart = input("Would you like to restart position monitoring? (y/n): ")

                if restart.lower() in ['y', 'yes']:
                    print("🔄 Restarting position monitoring...")
                    try:
                        await self.bot._start_position_monitoring_resilient()
                        print("✅ Position monitoring restarted successfully!")

                        # Send notification to Telegram
                        if hasattr(self.bot, 'signal_handler') and self.bot.signal_handler:
                            from datetime import datetime
                            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            notification_msg = (
                                f"🔄 POSITION MONITORING RESTART — {timestamp}\n"
                                f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                                f"✅ Position monitoring has been restarted manually via CLI.\n"
                                f"📊 Currently monitoring {len(open_positions)} open positions.\n"
                                f"🛡️ Max hold time protection: 5 minutes\n"
                                f"⚡ Health checker: Active\n\n"
                                f"All positions are now being monitored for TP/SL conditions."
                            )
                            try:
                                await self.bot.signal_handler.send_info_message(notification_msg)
                                print("✅ Restart notification sent to Telegram")
                            except Exception as e:
                                print(f"❌ Error sending notification: {e}")

                    except Exception as e:
                        print(f"❌ Error restarting position monitoring: {e}")
                else:
                    print("Position monitoring restart cancelled.")
            else:
                print("\n✅ Position monitoring is working properly!")
                print("🛡️ All systems are operational:")
                print("   • Monitor task: Running")
                print("   • Health checker: Active")
                print("   • Error count: 0")
                print("   • Max hold time protection: 5 minutes")

        except Exception as e:
            print(f"❌ Error checking position monitoring status: {e}")
            import traceback
            traceback.print_exc()

    async def _reload_configuration(self):
        """Reload configuration from finalconfig.json"""
        print("\n🔄 Reloading configuration from finalconfig.json...")
        try:
            # Reload the configuration
            self.bot.config.reload()

            # Get updated settings
            trading_settings = self.bot.config.get_section('trading_settings')
            strategy = trading_settings.get('strategy', 'default')

            rug_protection = trading_settings.get('rug_protection', {})
            min_liquidity = rug_protection.get('min_liquidity_usd', 7000)

            tp_sl_settings = self.bot.config.get_tp_sl_settings()
            take_profit = tp_sl_settings.get('pnl_sell_percent_max', 20.0)
            stop_loss = abs(tp_sl_settings.get('stop_loss_percent', -25.0))

            # FIXED: Read from correct location in trading_settings
            max_trades = trading_settings.get('max_concurrent_trades', 1)
            max_hold_time = trading_settings.get('max_hold_time_minutes', 3)

            print("✅ Configuration reloaded successfully!")
            print(f"📊 Strategy: {strategy}")
            print(f"💧 Min Liquidity: ${min_liquidity:,}")
            print(f"🎯 Take Profit: {take_profit}%")
            print(f"🛑 Stop Loss: -{stop_loss}%")
            print(f"🔢 Max Concurrent Trades: {max_trades}")
            print(f"⏱️ Max Hold Time: {max_hold_time} minutes")

        except Exception as e:
            print(f"❌ Error reloading configuration: {e}")