import re

# Test token
expected_token = "7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump"

# Simple test message with the └ symbol
test_line = "└7t7jHGG49LQ5EgTZHN49Mphy5vEn8KfkS64aaP4jpump"

print("Testing token extraction...")
print(f"Expected: {expected_token}")
print(f"Test line: {test_line}")

# Test the pump pattern
pump_pattern = r'([1-9A-HJ-NP-Za-km-z]{32,44}pump)'
match = re.search(pump_pattern, test_line)
if match:
    print(f"Found: {match.group(1)}")
    print(f"Correct: {match.group(1) == expected_token}")
else:
    print("No match found")
