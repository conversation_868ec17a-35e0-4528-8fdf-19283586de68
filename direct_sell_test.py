#!/usr/bin/env python3
"""
DIRECT SELL TEST - Test sell operations with existing tokens
"""

import asyncio
import logging
import time
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Tokens you already have in wallet
EXISTING_TOKENS = {
    "zByW1uVMmEmGpbr1yAgF9nxeDd6NKMRDbautoLwpump": 977.323978,
    "GkR6xgN7qfFxGdFBbj7hwT7NpwT7YkF4neXQeYLSpump": 47676.712127
}

async def test_direct_sell(token_address: str, expected_balance: float, trader) -> dict:
    """Test sell operation directly with existing tokens"""
    logger.info(f"=== DIRECT SELL TEST: {token_address[:8]}... ===")
    logger.info(f"Expected balance: {expected_balance:,.6f} tokens")
    
    result = {
        "token": token_address,
        "timestamp": datetime.now().isoformat(),
        "expected_balance": expected_balance,
        "sell_test": {},
        "success": False
    }
    
    try:
        # Get initial SOL balance
        initial_sol_balance = await trader.get_balance()
        logger.info(f"Initial SOL balance: {initial_sol_balance:.6f}")
        
        # TEST SELL OPERATION DIRECTLY
        logger.info(f"TESTING DIRECT SELL: {expected_balance:,.6f} tokens of {token_address[:8]}...")
        
        sell_start = time.time()
        sell_result = await trader.sell_token(
            token_address=token_address,
            token_amount=expected_balance,  # Use expected balance
            slippage_percent=5.0  # Higher slippage for better success rate
        )
        sell_time = time.time() - sell_start
        
        logger.info(f"Sell result: {sell_result}")
        
        if sell_result and sell_result.get('success', False):
            sol_received = sell_result.get('data', {}).get('sol_received', 0)
            sell_tx_sig = sell_result.get('data', {}).get('transaction_signature', 'N/A')
            
            logger.info(f"✅ SELL SUCCESS: {sol_received:.6f} SOL received")
            logger.info(f"   Transaction: {sell_tx_sig}")
            logger.info(f"   Execution time: {sell_time:.2f}s")
            
            # Check final SOL balance
            await asyncio.sleep(3)
            final_sol_balance = await trader.get_balance()
            net_sol_change = final_sol_balance - initial_sol_balance
            
            logger.info(f"VERIFICATION:")
            logger.info(f"   Initial SOL: {initial_sol_balance:.6f}")
            logger.info(f"   Final SOL: {final_sol_balance:.6f}")
            logger.info(f"   Net change: {net_sol_change:.6f} SOL")
            
            result["sell_test"] = {
                "success": True,
                "sol_received": sol_received,
                "transaction_signature": sell_tx_sig,
                "execution_time": sell_time,
                "verified": sell_result.get('verified', False),
                "net_sol_change": net_sol_change,
                "initial_sol": initial_sol_balance,
                "final_sol": final_sol_balance
            }
            
            result["success"] = True
            
        else:
            error = sell_result.get('error', 'Unknown error') if sell_result else 'No result'
            logger.error(f"❌ SELL FAILED: {error}")
            result["sell_test"] = {"success": False, "error": error}
    
    except Exception as e:
        logger.error(f"❌ TEST FAILED: {e}")
        result["error"] = str(e)
    
    logger.info(f"=== COMPLETED: {token_address[:8]}... ===\n")
    return result

async def run_direct_sell_tests():
    """Run direct sell tests for existing tokens"""
    logger.info("🚀 STARTING DIRECT SELL TESTS")
    logger.info("=" * 80)
    logger.info(f"Testing {len(EXISTING_TOKENS)} tokens with existing balances")
    logger.info("=" * 80)
    
    # Initialize trader
    try:
        from pumpportal_trader import PumpPortalTrader
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        trader = PumpPortalTrader(config_manager)
        logger.info("✅ Trader initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize trader: {e}")
        return
    
    # Run tests for each token
    all_results = {
        "test_suite": "Direct Sell Tests",
        "timestamp": datetime.now().isoformat(),
        "tokens_tested": len(EXISTING_TOKENS),
        "results": []
    }
    
    for i, (token_address, expected_balance) in enumerate(EXISTING_TOKENS.items(), 1):
        logger.info(f"🔄 TESTING TOKEN {i}/{len(EXISTING_TOKENS)}")
        
        try:
            result = await test_direct_sell(token_address, expected_balance, trader)
            all_results["results"].append(result)
            
            # Wait between tests
            if i < len(EXISTING_TOKENS):
                logger.info("⏳ Waiting 10 seconds before next test...")
                await asyncio.sleep(10)
                
        except Exception as e:
            logger.error(f"❌ Test failed for {token_address[:8]}: {e}")
            all_results["results"].append({
                "token": token_address,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "success": False
            })
    
    # Calculate statistics
    successful_tests = sum(1 for r in all_results["results"] if r.get("success", False))
    
    all_results["successful_tests"] = successful_tests
    all_results["success_rate"] = successful_tests / len(EXISTING_TOKENS) if EXISTING_TOKENS else 0
    
    # Print final summary
    logger.info("🏁 DIRECT SELL TESTS COMPLETED!")
    logger.info("=" * 80)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   Success Rate: {successful_tests}/{len(EXISTING_TOKENS)} ({all_results['success_rate']:.1%})")
    
    if successful_tests > 0:
        logger.info("✅ SELL OPERATIONS WORKING!")
    else:
        logger.error("❌ ALL SELL OPERATIONS FAILED!")
    
    logger.info("=" * 80)
    
    return all_results

if __name__ == "__main__":
    asyncio.run(run_direct_sell_tests())
