import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable, Tuple, Set
from datetime import datetime

logger = logging.getLogger(__name__)

class PriorityItem:
    """A class to represent an item in a priority queue with timestamp."""

    def __init__(self, item: Any, priority: int = 0, timestamp: float = None):
        self.item = item
        self.priority = priority
        self.timestamp = timestamp or time.time()

    def __lt__(self, other):
        """Compare items based on priority first, then timestamp."""
        if self.priority != other.priority:
            # Higher priority (lower number) comes first
            return self.priority < other.priority
        # If same priority, older items come first
        return self.timestamp < other.timestamp

class PriorityQueue:
    """A priority queue implementation with async support."""

    def __init__(self, maxsize: int = 0):
        self._queue = asyncio.PriorityQueue(maxsize=maxsize)
        self._unfinished_tasks = 0
        self._finished = asyncio.Event()
        self._finished.set()

    async def put(self, item: Any, priority: int = 0):
        """Put an item into the queue with a priority."""
        priority_item = PriorityItem(item, priority)
        await self._queue.put(priority_item)
        self._unfinished_tasks += 1
        self._finished.clear()

    async def get(self) -> Any:
        """Get an item from the queue."""
        priority_item = await self._queue.get()
        return priority_item.item

    def task_done(self):
        """Indicate that a formerly enqueued task is complete."""
        self._unfinished_tasks -= 1
        if self._unfinished_tasks <= 0:
            self._finished.set()

    async def join(self):
        """Block until all items in the queue have been processed."""
        await self._finished.wait()

    def qsize(self) -> int:
        """Return the approximate size of the queue."""
        return self._queue.qsize()

    def empty(self) -> bool:
        """Return True if the queue is empty, False otherwise."""
        return self._queue.empty()

    def full(self) -> bool:
        """Return True if the queue is full, False otherwise."""
        return self._queue.full()

class AnalysisWorkerPool:
    """A pool of workers for parallel token analysis."""

    def __init__(self, token_analyzer, num_workers: int = 8, queue_size: int = 100):
        """
        Initialize the analysis worker pool with optimized settings for meme trading.

        Args:
            token_analyzer: The token analyzer to use for analysis
            num_workers: Number of parallel workers (default: 8, increased from 3)
            queue_size: Maximum size of the analysis queue (default: 100, increased from 50)
        """
        self.token_analyzer = token_analyzer
        self.num_workers = num_workers

        # Fast analysis function reference (will be set by bot_controller)
        self.fast_analysis_function = None
        self.analysis_queue = PriorityQueue(maxsize=queue_size)
        self.result_callbacks: Dict[str, List[Callable]] = {}
        self.workers: List[asyncio.Task] = []
        self.is_running = False

        # Use more efficient data structures for token tracking
        # Only track currently processing tokens (no persistent "processed" cache for meme trading)
        self.processing_tokens: Set[str] = set()

        # Use a faster lock implementation for high-concurrency scenarios
        self.token_lock = asyncio.Lock()

        # Track active positions for prioritization
        self.active_positions: Set[str] = set()

        # Enhanced statistics for performance monitoring
        self.stats = {
            'total_processed': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'avg_processing_time': 0.0,
            'processing_times': [],
            'queue_wait_times': [],
            'avg_queue_wait_time': 0.0,
            'max_queue_size': 0,
            'current_queue_size': 0,
            'worker_utilization': 0.0,
            'last_update_time': time.time()
        }

    def set_fast_analysis_function(self, fast_analysis_func):
        """Set the fast analysis function for optimized token analysis"""
        self.fast_analysis_function = fast_analysis_func
        logger.info("✅ Fast analysis function set in worker pool")

    async def start(self):
        """Start the worker pool."""
        if self.is_running:
            logger.warning("Worker pool is already running")
            return

        self.is_running = True
        logger.info(f"Starting analysis worker pool with {self.num_workers} workers")

        # Create and start workers
        for i in range(self.num_workers):
            worker = asyncio.create_task(self._worker_loop(i))
            worker.set_name(f"analysis_worker_{i}")
            self.workers.append(worker)
            logger.info(f"Started analysis worker {i}")

    async def stop(self):
        """Stop the worker pool."""
        if not self.is_running:
            logger.warning("Worker pool is not running")
            return

        logger.info("Stopping analysis worker pool")
        self.is_running = False

        # Cancel all workers
        for worker in self.workers:
            worker.cancel()

        # Wait for all workers to finish
        try:
            await asyncio.gather(*self.workers, return_exceptions=True)
        except asyncio.CancelledError:
            pass

        self.workers = []
        logger.info("Analysis worker pool stopped")

    async def submit_analysis(self, token_address: str, metadata: Dict[str, Any] = None, priority: int = 0,
                           callback: Optional[Callable] = None, is_active_position: bool = False) -> bool:
        """
        Submit a token for analysis with optimized prioritization for meme trading.

        This implementation includes:
        - Special handling for active positions (highest priority)
        - Automatic priority adjustment based on token status
        - Efficient duplicate detection
        - Metadata enrichment for better analysis

        Args:
            token_address: The token address to analyze
            metadata: Additional metadata for the analysis
            priority: Base priority of the analysis (lower number = higher priority)
            callback: Optional callback function to call with the result
            is_active_position: Whether this token has an active position (affects priority)

        Returns:
            bool: True if the token was queued, False if it was rejected
        """
        if not token_address:
            logger.warning("Cannot submit analysis for empty token address")
            return False

        # Initialize metadata if not provided
        if metadata is None:
            metadata = {}

        # Add active position flag to metadata
        if is_active_position:
            metadata['is_active_position'] = True
            # Add to active positions set for future prioritization
            self.active_positions.add(token_address)

        # FIXED: Removed aggressive duplicate detection for meme coin trading
        # For volatile meme coins, every analysis request is valid since prices change constantly
        async with self.token_lock:
            # Only check if currently being processed (to avoid true duplicates in queue)
            if token_address in self.processing_tokens:
                # For active positions, allow concurrent processing for real-time updates
                if is_active_position:
                    logger.info(f"Active position {token_address} already being processed, allowing concurrent analysis for real-time updates")
                else:
                    # For regular tokens, only block if literally being processed right now
                    logger.info(f"Token {token_address} currently being processed, allowing anyway for fresh data")
                    # Don't return False - let it through for fresh analysis

            # Mark token as being processed
            self.processing_tokens.add(token_address)

        # Adjust priority based on token status
        effective_priority = priority

        # Active positions get highest priority (0)
        if is_active_position:
            effective_priority = 0
            logger.info(f"Setting highest priority (0) for active position {token_address}")

        # Create analysis task with enriched metadata
        analysis_task = {
            'token_address': token_address,
            'metadata': metadata,
            'timestamp': time.time(),
            'is_active_position': is_active_position
        }

        # Register callback if provided
        if callback:
            if token_address not in self.result_callbacks:
                self.result_callbacks[token_address] = []
            self.result_callbacks[token_address].append(callback)

        # Queue the analysis task with effective priority
        try:
            # For active positions, use put_nowait to avoid waiting if possible
            if is_active_position and not self.analysis_queue.full():
                # Try non-blocking put first
                try:
                    # Create a PriorityItem directly to avoid awaiting
                    priority_item = PriorityItem(analysis_task, effective_priority)
                    self.analysis_queue._queue.put_nowait(priority_item)
                    self.analysis_queue._unfinished_tasks += 1
                    self.analysis_queue._finished.clear()
                    logger.info(f"Fast-tracked analysis for active position {token_address} with priority {effective_priority}")
                    return True
                except asyncio.QueueFull:
                    # Fall back to normal put if queue is full
                    pass

            # Normal queuing path
            await self.analysis_queue.put(analysis_task, effective_priority)
            logger.info(f"Queued analysis for {token_address} with priority {effective_priority}")
            return True

        except asyncio.QueueFull:
            # Special handling for queue full condition
            if is_active_position:
                # For active positions, try to make room by removing a low priority item
                logger.warning(f"Analysis queue full, attempting to make room for active position {token_address}")
                # This would require custom queue implementation to remove items
                # For now, we'll just log and continue with normal rejection

            logger.warning(f"Analysis queue is full, rejecting {token_address}")
            # Remove token from processing set
            async with self.token_lock:
                self.processing_tokens.remove(token_address)
                # Also remove from active positions if it was one
                if token_address in self.active_positions:
                    self.active_positions.remove(token_address)
            return False

    async def _worker_loop(self, worker_id: int):
        """
        Optimized worker loop for processing token analyses with improved performance.

        This implementation includes:
        - Queue wait time tracking
        - Adaptive timeout scaling based on token priority
        - Optimized error handling with minimal locking
        - Efficient callback execution

        Args:
            worker_id: The ID of this worker
        """
        logger.info(f"Analysis worker {worker_id} started")

        # Track worker state for utilization metrics
        worker_busy = False
        last_state_change = time.time()
        total_busy_time = 0
        total_time = 0

        while self.is_running:
            try:
                # Update worker utilization stats
                current_time = time.time()
                time_delta = current_time - last_state_change
                if worker_busy:
                    total_busy_time += time_delta
                total_time += time_delta
                last_state_change = current_time

                # Update worker utilization in stats
                if total_time > 0:
                    self.stats['worker_utilization'] = total_busy_time / total_time

                # Get next analysis task with queue wait time tracking
                queue_wait_start = time.time()
                analysis_task = await self.analysis_queue.get()
                queue_wait_time = time.time() - queue_wait_start

                # Track queue wait time
                self.stats['queue_wait_times'].append(queue_wait_time)
                if len(self.stats['queue_wait_times']) > 100:
                    self.stats['queue_wait_times'] = self.stats['queue_wait_times'][-100:]
                self.stats['avg_queue_wait_time'] = sum(self.stats['queue_wait_times']) / len(self.stats['queue_wait_times'])

                # Update queue size stats
                current_queue_size = self.analysis_queue.qsize()
                self.stats['current_queue_size'] = current_queue_size
                if current_queue_size > self.stats['max_queue_size']:
                    self.stats['max_queue_size'] = current_queue_size

                # Extract task details
                token_address = analysis_task['token_address']
                metadata = analysis_task['metadata']
                task_timestamp = analysis_task.get('timestamp', 0)

                # Calculate task age for logging
                task_age = time.time() - task_timestamp

                # Set worker as busy
                worker_busy = True

                # Check if this is an active position (for logging only)
                is_active = token_address in self.active_positions
                priority_label = "HIGH" if is_active else "normal"

                # Log with priority information
                logger.info(f"Worker {worker_id} analyzing token {token_address} (priority: {priority_label}, age: {task_age:.2f}s)")
                start_time = time.time()

                try:
                    # Check if token is an active position and add flag to metadata
                    if is_active and 'is_active_position' not in metadata:
                        metadata['is_active_position'] = True

                    # Perform the analysis with optimized timeout handling
                    # For active positions, use a shorter timeout to ensure fast updates

                    # CRITICAL FIX: Only pass parameters that are accepted by TokenAnalyzer.analyze()
                    # Create a clean copy of metadata with only allowed parameters
                    # TokenAnalyzer.analyze() accepts 'is_active_position', 'extracted_metrics', 'skip_notifications', etc.
                    clean_metadata = {}

                    # Include is_active_position parameter
                    if 'is_active_position' in metadata:
                        clean_metadata['is_active_position'] = metadata['is_active_position']

                    # IMPORTANT: Include extracted_metrics parameter if available
                    if 'extracted_metrics' in metadata:
                        clean_metadata['extracted_metrics'] = metadata['extracted_metrics']
                        logger.info(f"Including extracted_metrics in TokenAnalyzer.analyze() call")

                    # Include is_gmgn_channel parameter if available
                    if 'is_gmgn_channel' in metadata:
                        clean_metadata['is_gmgn_channel'] = metadata['is_gmgn_channel']
                        logger.info(f"Including is_gmgn_channel={metadata['is_gmgn_channel']} in TokenAnalyzer.analyze() call")

                    # Include is_pumpfun_volume_alert parameter if available
                    if 'is_pumpfun_volume_alert' in metadata:
                        clean_metadata['is_pumpfun_volume_alert'] = metadata['is_pumpfun_volume_alert']
                        logger.info(f"Including is_pumpfun_volume_alert={metadata['is_pumpfun_volume_alert']} in TokenAnalyzer.analyze() call")

                    # Include source_channel_id for GMGN channel detection
                    if 'source_channel_id' in metadata:
                        clean_metadata['source_channel_id'] = metadata['source_channel_id']
                        logger.info(f"Including source_channel_id={metadata['source_channel_id']} for GMGN channel detection")

                    # Include source_channel for GMGN channel detection
                    if 'source_channel' in metadata:
                        clean_metadata['source_channel'] = metadata['source_channel']
                        logger.info(f"Including source_channel={metadata['source_channel']} for GMGN channel detection")

                    # Include source_type for GMGN channel detection
                    if 'source_type' in metadata:
                        clean_metadata['source_type'] = metadata['source_type']
                        logger.info(f"Including source_type={metadata['source_type']} for GMGN channel detection")

                    # Log what we're doing for debugging
                    logger.info(f"Cleaning metadata for TokenAnalyzer.analyze() - passing allowed parameters: {list(clean_metadata.keys())}")

                    # Store skip_notifications flag for later use, but don't pass it to analyze()
                    skip_notifications = False
                    if 'skip_notifications' in metadata:
                        skip_notifications = metadata['skip_notifications']

                        # Log if we're skipping notifications
                        if skip_notifications:
                            logger.info(f"Skipping notifications for {token_address} as requested")

                    # Use fast analysis only - simple_pump_analyzer is the single source of truth
                    if self.fast_analysis_function and callable(self.fast_analysis_function):
                        logger.info(f"Worker {worker_id} using fast analysis for {token_address}")
                        if is_active:
                            # Use asyncio.timeout for strict timeout enforcement
                            async with asyncio.timeout(3.0):  # 3 second timeout for active positions
                                result = await self.fast_analysis_function(token_address, **clean_metadata)
                        else:
                            # CRITICAL FIX: Apply timeout for regular tokens to prevent hanging
                            async with asyncio.timeout(10.0):  # 10 second timeout for regular tokens
                                result = await self.fast_analysis_function(token_address, **clean_metadata)

                        # Fast analysis provides complete results including trading logic
                        # No need to call token_analyzer - simple_pump_analyzer handles everything
                    else:
                        # No fallback - fast analysis is required
                        logger.error(f"Fast analysis function not available for {token_address}")
                        result = {"exists": False, "reason": "Fast analysis function not available"}

                    # Update statistics
                    processing_time = time.time() - start_time
                    self.stats['total_processed'] += 1
                    self.stats['processing_times'].append(processing_time)

                    # Keep only the last 100 processing times
                    if len(self.stats['processing_times']) > 100:
                        self.stats['processing_times'] = self.stats['processing_times'][-100:]

                    # Add skip_notifications flag to the result if it was in the metadata
                    if 'skip_notifications' in metadata:
                        result['skip_notifications'] = metadata['skip_notifications']

                    # Calculate average processing time using faster method
                    self.stats['avg_processing_time'] = sum(self.stats['processing_times']) / len(self.stats['processing_times'])

                    # Update success/failure stats
                    if result.get('exists', False):
                        self.stats['successful_analyses'] += 1
                        logger.info(f"Worker {worker_id} successfully analyzed {token_address} in {processing_time:.2f}s")
                    else:
                        self.stats['failed_analyses'] += 1
                        error_reason = result.get('error', result.get('reason', 'Unknown reason'))
                        logger.warning(f"Worker {worker_id} failed to analyze {token_address}: {error_reason}")

                    # Execute callbacks with batched lock acquisition
                    callbacks_to_execute = []
                    if token_address in self.result_callbacks:
                        # Copy callbacks to local list to minimize lock time
                        callbacks_to_execute = self.result_callbacks[token_address].copy()
                        # Remove callbacks after copying
                        del self.result_callbacks[token_address]

                    # Execute callbacks outside of any locks
                    for callback in callbacks_to_execute:
                        try:
                            # Ensure result is a dictionary before passing to callback
                            if result is None:
                                logger.warning(f"Analysis result for {token_address} is None, creating empty dict")
                                safe_result = {
                                    "exists": False,
                                    "reason": "Analysis returned None",
                                    "confidence": 0.0,
                                    "price": 0.0,
                                    "liquidity_usd": 0.0,
                                    "volume_5m": 0.0
                                }
                            elif isinstance(result, bool):
                                logger.warning(f"Analysis result for {token_address} is a boolean ({result}), converting to dict")
                                safe_result = {
                                    "exists": result,
                                    "reason": "Boolean result converted to dict",
                                    "confidence": 0.0 if not result else 0.7,
                                    "price": 0.0,
                                    "liquidity_usd": 0.0,
                                    "volume_5m": 0.0
                                }
                                # Add extracted_metrics if available in metadata
                                if 'extracted_metrics' in metadata:
                                    safe_result['extracted_metrics'] = metadata['extracted_metrics']
                            elif not isinstance(result, dict):
                                logger.warning(f"Analysis result for {token_address} is not a dict, converting to dict")
                                safe_result = {
                                    "exists": bool(result),
                                    "reason": f"Non-dict result ({type(result)}) converted to dict",
                                    "confidence": 0.0,
                                    "price": 0.0,
                                    "liquidity_usd": 0.0,
                                    "volume_5m": 0.0
                                }
                                # Add extracted_metrics if available in metadata
                                if 'extracted_metrics' in metadata:
                                    safe_result['extracted_metrics'] = metadata['extracted_metrics']
                            else:
                                safe_result = result
                                # Ensure required fields exist in the result
                                if 'exists' not in safe_result:
                                    safe_result['exists'] = True
                                if 'confidence' not in safe_result:
                                    safe_result['confidence'] = 0.7 if safe_result.get('exists', False) else 0.0
                                if 'price' not in safe_result:
                                    safe_result['price'] = 0.0
                                if 'liquidity_usd' not in safe_result:
                                    safe_result['liquidity_usd'] = 0.0
                                if 'volume_5m' not in safe_result:
                                    safe_result['volume_5m'] = 0.0

                            # Now execute the callback with the safe result
                            logger.info(f"Executing callback for {token_address} with safe_result: {safe_result}")
                            await callback(safe_result)
                        except Exception as e:
                            logger.error(f"Error in analysis callback for {token_address}: {e}", exc_info=True)

                    # Remove token from processing set (no persistent processed cache for meme trading)
                    async with self.token_lock:
                        if token_address in self.processing_tokens:
                            self.processing_tokens.remove(token_address)

                except asyncio.TimeoutError:
                    # Handle timeout specifically for better error reporting
                    timeout_duration = 3.0 if is_active else 10.0
                    logger.warning(f"Worker {worker_id} timed out analyzing {token_address} after {timeout_duration}s")
                    self.stats['failed_analyses'] += 1

                    # Execute callbacks with timeout error result
                    callbacks_to_execute = []
                    if token_address in self.result_callbacks:
                        callbacks_to_execute = self.result_callbacks[token_address].copy()
                        del self.result_callbacks[token_address]

                    # Create timeout error result
                    timeout_result = {
                        "exists": False,
                        "reason": f"Analysis timed out after {timeout_duration}s",
                        "confidence": 0.0,
                        "price": 0.0,
                        "liquidity_usd": 0.0,
                        "volume_5m": 0.0,
                        "error": "timeout"
                    }

                    # Execute callbacks to notify about timeout
                    for callback in callbacks_to_execute:
                        try:
                            logger.info(f"Executing timeout callback for {token_address}")
                            await callback(timeout_result)
                        except Exception as e:
                            logger.error(f"Error in timeout callback for {token_address}: {e}", exc_info=True)

                    # Remove from processing set to allow retries
                    async with self.token_lock:
                        if token_address in self.processing_tokens:
                            self.processing_tokens.remove(token_address)
                    logger.info(f"Removed {token_address} from processing set due to timeout - retries allowed")

                except Exception as e:
                    # Handle other exceptions
                    logger.error(f"Worker {worker_id} error analyzing {token_address}: {e}", exc_info=True)
                    self.stats['failed_analyses'] += 1

                    # Execute callbacks with error result
                    callbacks_to_execute = []
                    if token_address in self.result_callbacks:
                        callbacks_to_execute = self.result_callbacks[token_address].copy()
                        del self.result_callbacks[token_address]

                    # Create error result
                    error_result = {
                        "exists": False,
                        "reason": f"Analysis error: {str(e)}",
                        "confidence": 0.0,
                        "price": 0.0,
                        "liquidity_usd": 0.0,
                        "volume_5m": 0.0,
                        "error": str(e)
                    }

                    # Execute callbacks to notify about error
                    for callback in callbacks_to_execute:
                        try:
                            logger.info(f"Executing error callback for {token_address}")
                            await callback(error_result)
                        except Exception as callback_error:
                            logger.error(f"Error in error callback for {token_address}: {callback_error}", exc_info=True)

                    # Remove from processing set to allow retries
                    async with self.token_lock:
                        if token_address in self.processing_tokens:
                            self.processing_tokens.remove(token_address)
                    logger.info(f"Removed {token_address} from processing set due to error - retries allowed")

                # Mark task as done
                self.analysis_queue.task_done()

                # Set worker as idle
                worker_busy = False

            except asyncio.CancelledError:
                logger.info(f"Analysis worker {worker_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Error in analysis worker {worker_id}: {e}")
                # Use shorter sleep on error for faster recovery
                await asyncio.sleep(0.5)  # Reduced from 1.0s to 0.5s
                # Set worker as idle
                worker_busy = False

        logger.info(f"Analysis worker {worker_id} stopped")
